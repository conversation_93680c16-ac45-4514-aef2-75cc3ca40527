import { StackHeaderProps } from '@react-navigation/stack';
import { AnalyticsStackParamList } from './screens';
import { PlatformFactory } from '../../platform/PlatformFactory';
import { AnalyticsHeader } from '../../appComponents/header/AnalyticsHeader';
import getHeaderConfig from './getHeaderConfig';
import { OpportunityListStore, OpportunityReportsStore, TenantStore, UserStore } from '../../stores';
import { ResourcesStore } from '../../stores/ResourcesStore';

export default function (
  props: StackHeaderProps,
  userStore: UserStore,
  resourcesStore: ResourcesStore,
  opportunityReportsStore: OpportunityReportsStore,
  tenantStore: TenantStore,
  opportunityListStore: OpportunityListStore,
): React.ReactNode {
  // additionl router props are available here if needed
  const { route, navigation } = props;
  const router = PlatformFactory.getRouter(navigation);
  const appHeaderProps = getHeaderConfig(route.name as keyof AnalyticsStackParamList);

  function setEventFilterInfo() {
    const eventsCheckboxes = opportunityListStore.campaignListFilter.info;
    if (location.pathname.includes('overview')) {
      eventsCheckboxes.splice(0, 1);
      const noneIndex = eventsCheckboxes.findIndex((info) => info.label === 'None');
      if (noneIndex !== -1) {
        eventsCheckboxes.splice(noneIndex, 1);
      }
    }
    return eventsCheckboxes;
  }

  return (
    <AnalyticsHeader
      {...appHeaderProps}
      router={router}
      userStore={userStore}
      resourcesStore={resourcesStore}
      opportunityReportsStore={opportunityReportsStore}
      tenantStore={tenantStore}
      setEventFilterInfo={setEventFilterInfo}
    />
  );
}
