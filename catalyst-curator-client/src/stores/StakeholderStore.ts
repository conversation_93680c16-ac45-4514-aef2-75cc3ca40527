import { action, computed, makeObservable, observable, override } from 'mobx';
import { Store } from '../lib/stores/Store';
import { CreateStakeholderInput, SearchField, SearchOperator, Stakeholder } from '../services/codegen/types';
import { stakeholderService } from '../services/StakeholderService';

const DEFAULT_PAGE_SIZE = 10;

interface SearchValue {
  fieldName: string;
  value?: string;
}

interface SortValue {
  fieldName: string;
}
export class StakeholderStore extends Store {
  private searchStores: Record<string, StakeholderSearchStore> = {};

  constructor() {
    super();
    makeObservable(this, {});
  }

  getStakeholderSearchStore(name: string) {
    const searchStore = this.searchStores[name];
    if (searchStore) return searchStore;
    this.searchStores[name] = new StakeholderSearchStore();
    return this.searchStores[name];
  }

  async localClearAll(): Promise<void> {
    this.searchStores = {};
  }
  protected async localInitialize(): Promise<void> {}

  getStakeholder(input: { id?: string; name?: string; org?: string }): Promise<Stakeholder> {
    return this.call(async () => stakeholderService.getStakeholder(input));
  }

  async addStakeholder(stakeholder: { name?: string; org?: string }): Promise<Stakeholder> {
    const { name, org } = stakeholder;
    if (!name && !org) throw Error('name or org required for new stakeholder');
    const input = { name, org };
    const existingStakeholder = await this.call(async () => stakeholderService.getStakeholder(input));
    return existingStakeholder || this.call(async () => stakeholderService.createStakeholder(input));
  }

  removeStakeholder(id: string): void {
    // TODO: Stub for late date for admins to remove stakeholder entries.
  }
}
export class StakeholderSearchStore extends Store {
  private _matchedStakeholders: Array<Stakeholder> = [];
  private _searchValues: Record<string, string | undefined> = {};
  private _sortValue: SortValue = { fieldName: 'name' };
  private _pageSize: number = DEFAULT_PAGE_SIZE;

  constructor() {
    super();
    makeObservable<StakeholderSearchStore, '_matchedStakeholders' | '_searchValues' | '_pageSize' | '_sortValue'>(
      this,
      {
        _matchedStakeholders: observable,
        _searchValues: observable,
        _sortValue: observable,
        _pageSize: observable,
        queryStakeholders: action,
        sortField: computed,
        pageSize: computed,
        matchedStakeholders: computed,
        clearAll: override,
        clearCache: override,
        restore: override,
      },
    );
  }

  get matchedStakeholders(): Array<Stakeholder> {
    return this._matchedStakeholders;
  }

  getMatchedStakeholderById(id: string) {
    return this.matchedStakeholders.find((stakeholder) => stakeholder.id === id);
  }

  getMatchedStakeholder(fieldValues: SearchValue[]) {
    return this.matchedStakeholders.find((matchStakeholder) => {
      fieldValues.every((fieldValue) => {
        (matchStakeholder as any)[fieldValue.fieldName] === fieldValue.value ||
          (!(matchStakeholder as any)[fieldValue.fieldName] && !fieldValue.value);
      });
    });
  }

  uniqueMatches(fieldName: string): string[] {
    return this.matchedStakeholders.reduce((accum, stakeholder) => {
      const value = (stakeholder as any)[fieldName];
      if (value && !accum.includes(value)) accum.push(value);
      return accum;
    }, [] as string[]);
  }

  setFieldSearchValue(fieldSearchValue: SearchValue) {
    const { fieldName, value } = fieldSearchValue;
    this._searchValues[fieldName] = value;
  }

  getSearchValue(fieldName: string): string | undefined {
    return this._searchValues[fieldName];
  }

  set sortField(sortField: string) {
    this._sortValue.fieldName = sortField;
  }

  get sortField(): string {
    return this._sortValue.fieldName;
  }

  set pageSize(pageSize: number) {
    this._pageSize = pageSize;
  }

  get pageSize() {
    return this._pageSize;
  }

  async localClearAll(): Promise<void> {
    this._matchedStakeholders = [];
    this._searchValues = {};
    this._sortValue = { fieldName: 'name' };
    this._pageSize = DEFAULT_PAGE_SIZE;
  }
  protected async localInitialize(): Promise<void> {}

  async queryStakeholders(): Promise<void> {
    const searchFields: SearchField[] = [];
    Object.keys(this._searchValues).forEach((searchFieldName) => {
      if (this._searchValues[searchFieldName])
        searchFields.push({
          fieldNames: [searchFieldName],
          operator: SearchOperator.Match,
          searchValue: '^' + this._searchValues[searchFieldName],
        });
    });
    return searchFields.length
      ? this.call(async () => {
          this._matchedStakeholders = await stakeholderService.queryStakeholders({
            pagingInput: { pageSize: this._pageSize },
            searchSortInput: {
              searchFields,
              sortFields: [{ fieldName: this.sortField, ascending: true }],
            },
          });
        })
      : undefined;
  }
}
