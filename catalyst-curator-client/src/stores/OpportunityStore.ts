import { action, computed, makeObservable, observable } from 'mobx';
import { Validator } from '../lib';
import { Store } from '../lib/stores/Store';
import { attachmentService } from '../services/AttachmentService';
import {
  CreateExistingSolutionInput,
  CreateProjectFromOpportunityInput,
  Location,
  Opportunity,
  OpportunityOwner,
  OpportunityOwnerStatus,
  OpportunityStatus,
  RelatedOpportunityType,
  UpdateExistingSolutionInput,
  UpdateOperator,
  UpdateOpportunityInput,
  UpdateOpportunityLinks,
} from '../services/codegen/types';
import { opportunityService } from '../services/OpportunityService';
import { downloadManagerLocation, openWebFileStream } from '../utilities/File';
import { OpportunitySearchStore } from './OpportunitySearchStore';
import { ProjectStore } from './ProjectStore';
import {
  RelatedOpportunitySelectionType,
  checkRelatedOpportunityTypeEquivalence,
} from '../constants/RelatedOpportunity';
import { existingSolutionService } from '../services/ExistingSolutionService';

const validators: Record<string, Validator[]> = {};
class OpportunityStore extends Store {
  private static readonly stores: Record<string, OpportunityStore> = {};
  private searchStores: Record<string, OpportunitySearchStore> = {};

  private _opportunity?: Opportunity = undefined;
  private _buffer: Partial<Opportunity> = {};
  private _fileUploadInProgress = false;
  private _editable = false;
  searchValue = '';

  /*
    @TODO: consider clearing the referenced stores of data when
    not in view if memory becomes a problem
  */
  static getStoreForId(id: string): OpportunityStore {
    let opportunityStore = this.stores[id];
    if (!opportunityStore) {
      opportunityStore = new OpportunityStore(id);
      this.stores[id] = opportunityStore;
    }
    return opportunityStore;
  }

  static removeStoreForId(id: string): void {
    delete this.stores.id;
  }

  constructor(private readonly id: string) {
    super();
    makeObservable<
      OpportunityStore,
      '_opportunity' | '_buffer' | '_fileUploadInProgress' | 'setOpportunity' | '_editable'
    >(this, {
      _opportunity: observable,
      _buffer: observable,
      _fileUploadInProgress: observable,
      _editable: observable,
      searchValue: observable,
      refresh: action,
      setValue: action,
      setValues: action,
      setOpportunity: action,
      currentOwner: computed,
      addAttachment: action,
      deleteAttachment: action,
      opportunity: computed,
      hasBeenCurated: computed,
      addExistingSolution: action,
      updateExistingSolution: action,
      deleteExistingSolution: action,
      editable: computed,
    });
  }

  get hasBeenCurated(): boolean {
    return !!this.opportunity?.curationInfo?.lastCurated;
  }

  get currentOwner() {
    const currentOwner = this.opportunity?.owners?.find((owner) => owner.status === OpportunityOwnerStatus.Current);

    const initialOwner = this.opportunity?.owners?.find((owner) => owner.status === OpportunityOwnerStatus.Initial);
    if (currentOwner) return currentOwner;
    if (initialOwner) return initialOwner;
    return undefined;
  }

  get initialOwner(): OpportunityOwner | undefined {
    const initialOwner = this.opportunity?.owners.find((owner) => owner.status === OpportunityOwnerStatus.Initial);
    const areThereOtherOwners = this.opportunity?.owners.some(
      (owner) => owner.status !== OpportunityOwnerStatus.Initial,
    );
    if (initialOwner) {
      return initialOwner;
    } else {
      const user = this.opportunity?.user;
      if (!user) return undefined;
      return {
        addedAt: this.opportunity?.createdAt,
        id: user.id,
        createdAt: user.createdAt,
        isRemoved: areThereOtherOwners || false,
        opportunity: this.opportunity,
        status: OpportunityOwnerStatus.Initial,
        owner: {
          firstName: user?.firstName,
          lastName: user?.lastName,
          emailAddress: user?.emailAddress,
          createdAt: user.createdAt,
          org1: user?.org1,
          org2: user?.org2,
          org3: user?.org3,
          org4: user?.org4,
          phone: user?.phone,
          altContact: user?.altContact,
          id: user.id,
        },
      };
    }
  }

  get editable(): boolean {
    return this._editable;
  }

  set editable(editable: boolean) {
    this._editable = editable;
  }

  protected async localInitialize(): Promise<void> {
    return this.refresh();
  }

  refresh = async (): Promise<void> => {
    return this.call(async () => {
      const opportunity = await opportunityService.getOpportunity({ id: this.id });
      this.setOpportunity(opportunity);
    });
  };

  get opportunity(): Opportunity | undefined {
    if (this._opportunity) return { ...this._opportunity, ...this._buffer };
  }

  setValues(opportunityValues: UpdateOpportunityInput) {
    this.updateValues(this._buffer, opportunityValues);
  }

  setValue(name: string, value: any) {
    this.updateValue(this._buffer, name, value);
  }

  getValue(name: string, defaultValue?: string) {
    // Memoized for performance
    return computed(() => {
      const value = (this.opportunity as any)?.[name];
      return value || defaultValue;
    }).get();
  }

  getSubmissionValue(name: string, defaultValue?: string) {
    const value = (this.opportunity?.submissions?.[0] as any)?.[name];
    return value || defaultValue;
  }

  hasAnyOwned() {
    return !!this.opportunity?.ownedOpportunities?.length;
  }

  hasAnyOwnedLinks() {
    return !!this.opportunity?.ownedOpportunities.some(
      (relatedOpportunity) => relatedOpportunity.type === RelatedOpportunityType.Linked,
    );
  }

  hasAnyOwning() {
    return this.opportunity?.owningOpportunities?.length;
  }

  hasAnyParent() {
    return !!this.opportunity?.owningOpportunities.some(
      (relatedOpportunity) => relatedOpportunity.type === RelatedOpportunityType.Child,
    );
  }
  getParent() {
    return this.opportunity?.owningOpportunities!.find(
      (relatedOpportunity) => relatedOpportunity.type === RelatedOpportunityType.Child,
    );
  }

  hasAnyChildren() {
    return !!this.opportunity?.ownedOpportunities.some(
      (relatedOpportunity) => relatedOpportunity.type === RelatedOpportunityType.Child,
    );
  }

  hasParentRelationship(relatedId: string): boolean {
    return !!this.opportunity?.owningOpportunities.some(
      (relatedOpportunity) =>
        relatedOpportunity.source.id === relatedId && relatedOpportunity.type === RelatedOpportunityType.Child,
    );
  }

  hasOwnedRelationship(relatedId: string, type: RelatedOpportunitySelectionType): boolean {
    return !!this.opportunity?.ownedOpportunities.some(
      (relatedOpportunity) =>
        relatedOpportunity.target.id === relatedId &&
        checkRelatedOpportunityTypeEquivalence(type, relatedOpportunity.type),
    );
  }

  isLinkedToBy(relatedId: string): boolean {
    return !!this.opportunity?.owningOpportunities.some(
      (relatedOpportunity) =>
        relatedOpportunity.source.id === relatedId && relatedOpportunity.type === RelatedOpportunityType.Linked,
    );
  }

  async saveOpportunity(): Promise<void> {
    if (!this.hasErrors) {
      await this.call<void>(async () => {
        const opportunityResult = await opportunityService.updateOpportunity({ input: this._buffer, id: this.id });
        this.mergeOpportunity(opportunityResult as Opportunity);
      });
    }
  }

  async updateOpportunityLinks(updateLinks?: UpdateOpportunityLinks): Promise<void> {
    await this.call(async () => {
      await opportunityService.updateOpportunity({ input: {} as Opportunity, id: this.id, links: updateLinks });
    });
    return this.refresh();
  }

  // this assume we can only have one parent for now (this is not a restriction on the server-side)
  async removeParentRelationship(): Promise<void> {
    const owningRelatedOpportunity = this.getParent();
    if (owningRelatedOpportunity)
      return this.updateOwningRelationship(
        owningRelatedOpportunity.source.id,
        UpdateOperator.Rm,
        RelatedOpportunityType.Child,
      );
  }

  async addParentRelationship(parentId: string): Promise<void> {
    if (!this.hasAnyParent())
      return this.updateOwningRelationship(parentId, UpdateOperator.Add, RelatedOpportunityType.Child);
  }

  // these can child or linked relationships since both have an 'owning' side
  async updateOwningRelationship(
    owningId: string,
    operator: UpdateOperator,
    type: RelatedOpportunityType,
  ): Promise<void> {
    await this.call(async () => {
      const items = [{ id: this.id, type }];
      const objectUpdate: { [k: string]: any } = {};
      objectUpdate['relatedOpportunities'] = [{ operator, items }];
      await opportunityService.updateOpportunity({ input: {} as Opportunity, id: owningId, links: objectUpdate });
    });
    return this.refresh();
  }

  async softDeleteOpportunity() {
    this.setValues({ status: OpportunityStatus.Deleted });
    return this.saveOpportunity();
  }

  async addAttachment({ file, uri }: { file: File; uri: string }): Promise<void> {
    await this.call(async () => {
      this._fileUploadInProgress = true;
      try {
        const blob = await fetch(uri).then((response) => response.blob());
        const blobFile = new File([blob], file.name, { lastModified: file.lastModified, type: file.type });
        await attachmentService.uploadAttachment({ input: blobFile, links: { opportunityId: this.id } });
      } finally {
        this._fileUploadInProgress = false;
      }
    });
    return this.refresh();
  }

  async addExistingSolution(input: CreateExistingSolutionInput) {
    await this.call(async () => {
      await existingSolutionService.createExistingSolution({ input, links: { opportunityId: this.id } });
    });
    return this.refresh();
  }

  async updateExistingSolution(id: string, input: UpdateExistingSolutionInput) {
    await this.call(async () => {
      await existingSolutionService.updateExistingSolution({ id, input });
    });
    return this.refresh();
  }

  async deleteExistingSolution(id: string) {
    await this.call(async () => {
      await existingSolutionService.deleteExistingSolution({ id });
    });
    return this.refresh();
  }

  async deleteAttachment(attachmentId: string): Promise<void> {
    return this.call(async () => {
      const deleted = await attachmentService.deleteAttachment({ id: attachmentId });
      deleted && this.refresh();
    });
  }

  async getAttachment(attachmentId: string): Promise<Location> {
    return this.call(async () => {
      return attachmentService.getAttachment({ id: attachmentId });
    });
  }

  get fileUploadInProgress() {
    return this._fileUploadInProgress;
  }

  async createProjectFromOpportunity(input: Partial<CreateProjectFromOpportunityInput>): Promise<ProjectStore> {
    return this.call(() =>
      ProjectStore.createProjectFromOpportunity({
        ...input,
        opportunityId: this.id,
      } as CreateProjectFromOpportunityInput),
    );
  }

  getProjectIds(): string[] | undefined {
    return this._opportunity?.projects.map((project) => project.id);
  }

  async localClearAll(): Promise<void> {
    this.clearSearchStores();
  }

  getOpportunitySearchStore(name: string) {
    const searchStore = this.searchStores[name];
    if (searchStore) return searchStore;
    this.searchStores[name] = new OpportunitySearchStore();
    return this.searchStores[name];
  }

  async clearSearchStores(): Promise<void> {
    this.searchStores = {};
  }

  async handleDownload(fileName: string) {
    try {
      await openWebFileStream(`${downloadManagerLocation}/opp/${this.id}`, fileName);
    } catch (e: any) {
      this.addError(e);
    }
  }

  private setFileUploadInProgress(uploadInProgress = true) {
    this._fileUploadInProgress = uploadInProgress;
  }

  private mergeOpportunity(opportunity: Partial<Opportunity>) {
    this._opportunity && this.setOpportunity({ ...this._opportunity, ...this._buffer, ...opportunity });
  }

  private clearBuffer() {
    this._buffer = {};
    this.clearErrors();
  }

  private setOpportunity(opportunityData: Opportunity) {
    this._opportunity = opportunityData;
    this.clearBuffer();
  }
}

export default OpportunityStore;
