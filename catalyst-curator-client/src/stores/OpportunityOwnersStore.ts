import { action, computed, makeObservable, observable, runInAction } from 'mobx';
import { Store } from '../lib/stores/Store';
import {
  OpportunityOwner,
  Owner,
  PageInfo,
  PagingInput,
  SearchOperator,
  SearchOwnerResult,
  SearchSortInput,
} from '../services/codegen/types';
import { opportunityOwnerService } from '../services/OpportunityOwnerService';
import { emailValidator, notEmptyValidator, Validator } from '../lib';

const validators: Record<string, Validator[]> = {
  firstName: [notEmptyValidator],
  lastName: [notEmptyValidator],
  emailAddress: [notEmptyValidator, emailValidator],
};
export class OpportunityOwnersStore extends Store {
  static readonly DEFAULT_PAGE_SIZE = 500;
  static readonly DEFAULT_SEARCH_SORT: SearchSortInput = {
    sortFields: [{ fieldName: 'lastName', ascending: true }],
  };

  private _opportunityOwnersSearchResult: SearchOwnerResult[] = [];
  private _pageInfo: PageInfo = {} as PageInfo;
  private _searchSortInput: SearchSortInput = OpportunityOwnersStore.DEFAULT_SEARCH_SORT;
  private _pagingInput: PagingInput = { pageSize: OpportunityOwnersStore.DEFAULT_PAGE_SIZE, cursor: '0' };
  private _keywordSearchValue: string = '';
  private _buffer: Record<string, any> = {};
  private _opportunityOwner: Partial<OpportunityOwner> = {};

  constructor() {
    super(validators);
    makeObservable<
      OpportunityOwnersStore,
      '_opportunityOwnersSearchResult' | '_pageInfo' | '_keywordSearchValue' | '_opportunityOwner' | '_buffer'
    >(this, {
      _opportunityOwnersSearchResult: observable.shallow,
      _opportunityOwner: observable,
      _buffer: observable,
      _pageInfo: observable,
      _keywordSearchValue: observable,
      searchOwners: action,
      addOpportunityOwner: action,
      setSelectedSearchOwnerResult: action,
    });
  }

  setSelectedSearchOwnerResult(value: SearchOwnerResult) {
    this.setValue('firstName', value.firstName);
    this.setValue('lastName', value.lastName);
    this.setValue('emailAddress', value.emailAddress);
    this.setValue('phone', value.phone);
    this.setValue('org1', value.org1);
    this.setValue('org2', value.org2);
    this.setValue('org3', value.org3);
    this.setValue('org4', value.org4);
    this.setValue('altContact', value.altContact);
    this.setValue('organizationRole', value.organizationRole);
  }
  get owner(): Owner {
    return { ...this._opportunityOwner, ...(this._buffer as any) };
  }

  get opportunityOwnersSearchResult() {
    return this._opportunityOwnersSearchResult;
  }

  validateAll(): boolean {
    return super.validateAll(this._buffer);
  }

  get pageInfo(): PageInfo {
    return this._pageInfo;
  }

  set keywordSearchValue(value: string) {
    this._keywordSearchValue = value;
    this._searchSortInput = !!value
      ? {
          searchFields: [
            {
              fieldNames: ['firstName', 'lastName', 'emailAddress'],
              operator: SearchOperator.Match,
              searchValue: value,
            },
          ],
        }
      : OpportunityOwnersStore.DEFAULT_SEARCH_SORT;
  }

  get keywordSearchValue(): string {
    return this._keywordSearchValue;
  }

  async addOpportunityOwner(opportunityId: string): Promise<OpportunityOwner> {
    return this.call(async () => opportunityOwnerService.addOwner({ ...this.owner, opportunityId }));
  }

  async searchOwners(): Promise<SearchOwnerResult[]> {
    return this.call<SearchOwnerResult[]>(async () => {
      const { results, pageInfo } = await opportunityOwnerService.searchOwners({
        searchSortInput: this._searchSortInput,
        pagingInput: this._pagingInput,
      });
      runInAction(() => {
        this._opportunityOwnersSearchResult = results as SearchOwnerResult[];
        this._pageInfo = pageInfo;
      });
      return results as SearchOwnerResult[];
    });
  }

  async toggleRemovedOwner(id: string) {
    return this.call(async () => opportunityOwnerService.toggleRemovedOwner(id));
  }
  setValue(name: string, value: any) {
    this.updateValue(this._buffer, name, value);
  }

  getValue(name: string, defaultValue?: any) {
    const value = (this.owner as any)?.[name];
    return value || defaultValue;
  }

  protected async localInitialize(): Promise<void> {}

  async localClearAll(): Promise<void> {
    this._opportunityOwnersSearchResult = [];
  }
}
