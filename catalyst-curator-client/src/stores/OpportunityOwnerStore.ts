import { Store } from '../lib/stores/Store';
import { OpportunityOwner } from '../services/codegen/types';
import { opportunityOwnerService } from '../services/OpportunityOwnerService';

export class OpportunityOwnerStore extends Store {
  async addOpportunityOwner(input: { opportunityId: string; userId: string }): Promise<OpportunityOwner> {
    const { opportunityId, userId } = input;
    if (!opportunityId || !userId) {
      throw new Error('opportunityId and userId are required to add an opportunity owner');
    }
    return this.call(async () => opportunityOwnerService.addOwner(input));
  }

  protected async localInitialize(): Promise<void> {}

  async localClearAll(): Promise<void> {}
}
