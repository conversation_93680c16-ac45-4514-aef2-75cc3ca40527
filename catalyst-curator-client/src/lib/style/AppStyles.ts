import { Platform, StyleSheet } from 'react-native';
import { TenantFontSizes } from '../types';
import { MarginsStyle, PaddingsStyle, Styles, ComponentsStyle, BordersStyle, DefaultValues } from './Styles';

const _baseStyles = (() => {
  const marginSizes: Record<string, number> = {
    XS: 3,
    S: 5,
    MS: 8,
    M: 10,
    ML: 20,
    L: 30,
    XL: 80,
    XXL: 160,
  };
  const marginPrefixes = ['', 'Top', 'Bottom', 'Right', 'Left', 'Vertical', 'Horizontal'];
  const margins: StyleSheet.NamedStyles<MarginsStyle> = marginPrefixes.reduce((margins, prefix) => {
    Object.keys(marginSizes).forEach((key) => {
      margins[`${prefix}${key}` as keyof MarginsStyle] = {
        [`margin${prefix}`]: marginSizes[key],
      };
    });
    return margins;
  }, {} as MarginsStyle);
  margins.None = {
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 0,
    marginRight: 0,
  };

  const paddingSizes: Record<string, number> = {
    XS: 3,
    S: 5,
    MS: 8,
    M: 10,
    ML: 20,
    L: 30,
    XL: 80,
    XXL: 160,
  };
  const paddingPrefixes = ['', 'Top', 'Bottom', 'Right', 'Left', 'Vertical', 'Horizontal'];
  const paddings: StyleSheet.NamedStyles<PaddingsStyle> = paddingPrefixes.reduce((paddings, prefix) => {
    Object.keys(paddingSizes).forEach((key) => {
      paddings[`${prefix}${key}` as keyof PaddingsStyle] = {
        [`padding${prefix}`]: paddingSizes[key],
      };
    });
    return paddings;
  }, {} as PaddingsStyle);
  paddings.None = {
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: 0,
    paddingRight: 0,
  };

  const bordersPrimary = {
    borderWidth: 1,
    borderRadius: 8,
  };

  const bordersComponent = {
    borderWidth: 1,
    borderRadius: 5,
  };

  const buttonStyle = {
    borderWidth: 1,
    borderRadius: 4,
    minWidth: 16,
    elevation: 0,
  };

  const buttonTextStyle = {
    letterSpacing: 1.5,
    textTransform: 'uppercase',
  };

  const shadow = {
    shadowColor: '#aaa',
    shadowOffset: { width: 1, height: 1 },
    shadowRadius: 5,
    shadowOpacity: 0.5,
  };

  return {
    marginSizes,
    marginPrefixes,
    margins,
    paddingSizes,
    paddingPrefixes,
    paddings,
    bordersPrimary,
    bordersComponent,
    buttonStyle,
    buttonTextStyle,
    shadow,
  };
})();

// to override defaults see default values static instance in AppStyles
const _defaultValues = {
  defaultComponentWidth: 400,
  defaultComponentHeight: 38,
  smallComponentWidth: 350,
  largeComponentWidth: 680,
  primaryBorderRadius: _baseStyles.bordersPrimary.borderRadius,
  componentBorderRadius: _baseStyles.bordersComponent.borderRadius,
  iconButtonSize: 47,
  smallIconButtonSize: 25,
  maxWidth: 1440,
  groupTitleContentWidth: 360,
};

// for use when passing overrides to generate
export interface OverrideStyles {
  borders?: StyleSheet.NamedStyles<Partial<BordersStyle>>;
  margins?: StyleSheet.NamedStyles<Partial<MarginsStyle>>;
  paddings?: StyleSheet.NamedStyles<Partial<PaddingsStyle>>;
  fonts?: StyleSheet.NamedStyles<Partial<ReactNativePaper.ThemeFonts>>;
  fontSizes?: StyleSheet.NamedStyles<Partial<TenantFontSizes>>;
  components?: StyleSheet.NamedStyles<Partial<ComponentsStyle>>;
  defaultValues?: Partial<DefaultValues>;
}

export class AppStyles {
  static styles: Styles;
  // set before calling generate to override these
  static baseStyles = _baseStyles;
  // set before calling generate to override these
  static defaultValues = _defaultValues;

  static generate(theme: ReactNativePaper.ThemeProp, overrides?: OverrideStyles): Styles {
    const outlineStyle = Platform.select({
      web: { outlineColor: theme.colors.activeHighlight },
    });

    const { margins, paddings, bordersPrimary, bordersComponent, buttonStyle, buttonTextStyle, shadow } =
      AppStyles.baseStyles;

    const fonts: StyleSheet.NamedStyles<ReactNativePaper.ThemeFonts> = {
      regular: theme.fonts.regular,
      bold: theme.fonts.bold,
      medium: theme.fonts.medium,
      light: theme.fonts.light,
      thin: theme.fonts.thin,
      regularTitle: theme.fonts.regularTitle,
      mediumTitle: theme.fonts.mediumTitle,
      boldTitle: theme.fonts.boldTitle,
      lightTitle: theme.fonts.lightTitle,
      thinTitle: theme.fonts.thinTitle,
      ...overrides?.fonts,
    };
    const fontSizes: StyleSheet.NamedStyles<TenantFontSizes> = {
      xxxLarge: theme.fontSizes.xxxLarge,
      xxLarge: theme.fontSizes.xxLarge,
      xLarge: theme.fontSizes.xLarge,
      large: theme.fontSizes.large,
      mediumLarge: theme.fontSizes.mediumLarge,
      medium: theme.fontSizes.medium,
      mediumSmall: theme.fontSizes.mediumSmall,
      small: theme.fontSizes.small,
      xSmall: theme.fontSizes.xSmall,
      xxSmall: theme.fontSizes.xxSmall,
      xxxSmall: theme.fontSizes.xxxSmall,
      ...overrides?.fontSizes,
    };

    const components: StyleSheet.NamedStyles<ComponentsStyle> = {
      pageContainerConstraints: {
        flexDirection: 'row',
        justifyContent: 'center',
      },
      flexAll: {
        flex: 1,
        alignItems: 'stretch',
      },
      pageContentConstraints: {
        flexDirection: 'column',
        alignItems: 'center',
      },
      pageConstraints: {
        ...margins.RightL,
        ...margins.LeftL,
        ...paddings.TopL,
      },
      globalPageConstraints: {
        maxWidth: AppStyles.defaultValues.maxWidth,
      },
      compactPageStyle: {
        ...margins.RightXL,
        ...margins.LeftXL,
        ...paddings.TopML,
        ...paddings.HorizontalXL,
        flex: 1,
        alignItems: 'stretch',
      },
      panelStyle: {
        ...bordersPrimary,
        ...margins.HorizontalML,
        ...margins.TopML,
        ...paddings.VerticalML,
        ...paddings.HorizontalML,
        backgroundColor: theme.colors.surface,
        borderColor: theme.colors.border,
      },
      sectionStyle: {
        ...paddings.LeftML,
        ...paddings.RightML,
        ...paddings.TopML,
        ...paddings.BottomM,
        ...margins.BottomL,
      },
      minSectionStyle: {
        ...paddings.LeftML,
        ...paddings.RightML,
        ...paddings.TopS,
        ...paddings.BottomS,
        ...margins.BottomML,
      },
      groupTitleStyle: {
        ...fonts.regularTitle,
        ...fontSizes.mediumLarge,
        ...margins.BottomML,
        color: theme.colors.brandFontColor,
      },
      groupStyle: {
        ...margins.BottomL,
        ...paddings.BottomML,
        ...margins.LeftXL,
        ...margins.RightXXL,
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderColor: theme.colors.lightGray,
      },
      groupTitleContentStyle: {
        maxWidth: AppStyles.defaultValues.groupTitleContentWidth,
        flex: 1,
      },
      infoSectionStyle: {
        borderColor: theme.colors.lightGray,
        backgroundColor: theme.colors.textInfoBackground,
        borderTopWidth: 1,
        borderBottomWidth: 1,
      },
      rowStyle: {
        ...margins.VerticalS,
      },
      elementStyle: {
        ...margins.XS,
      },
      buttonPrimaryStyle: {
        ...buttonStyle,
        justifyContent: 'center',
        backgroundColor: theme.colors.buttonPrimary,
        height: AppStyles.defaultValues.defaultComponentHeight,
        maxHeight: AppStyles.defaultValues.defaultComponentHeight,
      },
      buttonContentStyle: {
        ...paddings.None,
        height: AppStyles.defaultValues.defaultComponentHeight,
        maxHeight: AppStyles.defaultValues.defaultComponentHeight,
        marginRight: 16,
      },
      buttonContentNoIconStyle: {
        ...paddings.LeftS,
        marginRight: 13,
      },
      buttonPrimaryTextStyle: {
        ...(buttonTextStyle as any),
        ...fonts.medium,
        ...fontSizes.mediumSmall,
        ...margins.None,
        ...margins.LeftMS,
        color: theme.colors.buttonPrimaryText,
      },
      buttonSecondaryStyle: {
        ...buttonStyle,
        justifyContent: 'center',
        borderColor: theme.colors.border,
        backgroundColor: theme.colors.buttonSecondary,
        height: AppStyles.defaultValues.defaultComponentHeight,
        maxHeight: AppStyles.defaultValues.defaultComponentHeight,
      },
      buttonSecondaryTextStyle: {
        ...(buttonTextStyle as any),
        ...fonts.medium,
        ...fontSizes.mediumSmall,
        ...margins.None,
        ...margins.LeftMS,
        color: theme.colors.buttonSecondaryText,
      },
      buttonTertiaryStyle: {
        ...buttonStyle,
        justifyContent: 'center',
        borderColor: theme.colors.border,
        backgroundColor: theme.colors.background,
        height: AppStyles.defaultValues.defaultComponentHeight,
        maxHeight: AppStyles.defaultValues.defaultComponentHeight,
      },
      buttonTertiaryTextStyle: {
        ...(buttonTextStyle as any),
        ...fonts.medium,
        ...fontSizes.mediumSmall,
        ...margins.None,
        ...margins.LeftMS,
        color: theme.colors.text,
      },
      buttonCompactStyle: {
        ...buttonStyle,
        width: undefined,
      },
      buttonCompactTextStyle: {
        ...(buttonTextStyle as any),
        ...fonts.medium,
        ...fontSizes.mediumSmall,
        ...margins.VerticalXS,
        ...margins.LeftM,
      },
      auxButton1Style: {
        ...buttonStyle,
        width: undefined,
        backgroundColor: theme.colors.surface,
        borderColor: theme.colors.text,
      },
      auxButton1TextStyle: {
        ...(buttonTextStyle as any),
        ...fonts.medium,
        ...fontSizes.mediumSmall,
        color: theme.colors.text,
      },
      auxButton2Style: {
        ...buttonStyle,
        ...paddings.None,
        width: undefined,
        backgroundColor: theme.colors.backgroundDark,
      },
      auxButton2TextStyle: {
        ...(buttonTextStyle as any),
        ...fonts.medium,
        ...fontSizes.mediumSmall,
        color: theme.colors.buttonPrimaryText,
      },
      auxButton3Style: {
        ...buttonStyle,
        ...paddings.None,
        width: undefined,
        backgroundColor: theme.colors.set1Color1,
      },
      auxButton3TextStyle: {
        ...(buttonTextStyle as any),
        ...fonts.medium,
        ...fontSizes.mediumSmall,
        color: theme.colors.buttonPrimaryText,
      },
      fabBarStyle: {
        position: 'absolute',
        bottom: 30,
        right: 20,
      },
      systemMessageContainerStyle: {
        ...paddings.M,
        ...bordersComponent,
        position: 'absolute',
        bottom: 30,
        right: 30,
        maxWidth: AppStyles.defaultValues.smallComponentWidth,
        minWidth: 250,
        minHeight: 50,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignContent: 'center',
      },
      textInputStyle: {
        ...paddings.VerticalMS,
        ...paddings.HorizontalXS,
        ...fonts.regular,
        ...fontSizes.small,
        width: '100%',
        ...bordersComponent,
        borderWidth: 0,
        color: theme.colors.text,
        ...outlineStyle,
        minHeight: AppStyles.defaultValues.defaultComponentHeight,
      },
      textInputReadOnlyStyle: {
        ...paddings.VerticalMS,
        ...paddings.HorizontalXS,
        ...fonts.regular,
        ...fontSizes.small,
        ...bordersComponent,
        color: theme.colors.text,
        borderWidth: 0,
        ...outlineStyle,
        width: '100%',
      },
      textInputReadOnlyPlaceholderStyle: {
        color: theme.colors.placeholder,
      },
      textInputContainerStyle: {
        maxWidth: AppStyles.defaultValues.largeComponentWidth,
        ...bordersComponent,
        borderColor: theme.colors.border,
        backgroundColor: theme.colors.surface,
      },
      textInputReadOnlyContainerStyle: {
        maxWidth: AppStyles.defaultValues.largeComponentWidth,
        ...bordersComponent,
        borderColor: 'rgba(255, 255, 255, 0)',
      },
      textInputIconStyle: {
        ...paddings.VerticalXS,
        ...margins.RightXS,
        color: theme.colors.darkGray,
      },
      searchInputContainerStyle: {
        ...bordersComponent,
        borderColor: theme.colors.border,
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxWidth: AppStyles.defaultValues.largeComponentWidth,
      },
      searchInputSearchBarStyle: {
        elevation: 0,
        flex: 1,
        ...bordersComponent,
        borderWidth: 0,
        height: 38,
        maxHeight: 38,
        backgroundColor: theme.colors.surface,
      },
      searchInputTextStyle: {
        ...fonts.regular,
        ...fontSizes.small,
        ...outlineStyle,
        height: 38,
        maxHeight: 38,
        minHeight: 38,
      },
      searchInputButtonStyle: {
        borderTopLeftRadius: 0,
        borderBottomLeftRadius: 0,
        borderRightWidth: 0,
        borderTopWidth: 0,
        borderBottomWidth: 0,
        height: 38,
        maxHeight: 38,
      },
      dateInputStyle: {
        backgroundColor: theme.colors.surface,
        margin: 0,
        padding: 0,
        ...outlineStyle,
      },
      dateInputContainerStyle: {
        ...bordersComponent,
        borderColor: theme.colors.border,
      },
      labelStyle: {
        ...theme.fonts.regular,
        ...theme.fontSizes.mediumSmall,
        color: theme.colors.text,
      },
      titleStyle: {
        ...fonts.regularTitle,
        ...fontSizes.mediumLarge,
        color: theme.colors.text,
      },
      helperTextInfoStyle: {
        ...fonts.regular,
        ...fontSizes.xSmall,
        color: theme.colors.info,
      },
      helperTextErrorStyle: {
        ...fonts.regular,
        ...fontSizes.xSmall,
        color: theme.colors.error,
      },
      messageInfoStyle: {
        ...fonts.regular,
        ...fontSizes.small,
        color: theme.colors.info,
      },
      messageErrorStyle: {
        ...fonts.regular,
        ...fontSizes.small,
        color: theme.colors.error,
      },
      dialogStyle: {
        backgroundColor: theme.colors.background,
        alignSelf: 'center',
        borderRadius: 16,
      },
      menuStyle: {
        maxWidth: AppStyles.defaultValues.smallComponentWidth,
        minWidth: 300,
        overflow: 'scroll',
        elevation: 0,
        backgroundColor: theme.colors.light,
      },
      menuItemStyle: {},
      menuTextStyle: {
        ...fonts.regular,
        ...fontSizes.small,
        color: theme.colors.text,
      },
      labeledInputStyle: {
        flexDirection: 'column',
      },
      labeledValueStyle: {
        flexDirection: 'row',
      },

      dropDownMenuAnchorStyle: {
        ...buttonStyle,
        ...bordersComponent,
        ...AppStyles.baseStyles.paddings.HorizontalMS,
        maxWidth: AppStyles.defaultValues.defaultComponentWidth,
        borderColor: theme.colors.border,
        backgroundColor: theme.colors.surface,
        height: AppStyles.defaultValues.defaultComponentHeight,
        maxHeight: AppStyles.defaultValues.defaultComponentHeight,
      },
      dropDownMenuReadOnlyAnchorStyle: {
        ...paddings.XS,
        maxWidth: AppStyles.defaultValues.defaultComponentWidth,
        height: AppStyles.defaultValues.defaultComponentHeight,
        maxHeight: AppStyles.defaultValues.defaultComponentHeight,
      },
      dropDownMenuAnchorTextStyle: {
        ...fonts.regular,
        ...fontSizes.small,
        flexWrap: 'nowrap',
      },
      hrStyle: {
        height: 1,
        borderTopWidth: 1,
        borderRightWidth: 0,
        borderLeftWidth: 0,
        borderBottomWidth: 0,
        borderColor: theme.colors.lightGray,
        borderStyle: 'solid',
      },
      vrStyle: {
        width: 1,
        borderTopWidth: 0,
        borderRightWidth: 0,
        borderLeftWidth: 1,
        borderBottomWidth: 0,
        borderColor: theme.colors.lightGray,
        borderStyle: 'solid',
      },
      circle: {
        borderRadius: 25,
        borderColor: theme.colors.border,
        backgroundColor: theme.colors.text,
      },
      tag: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: theme.colors.surface,
        ...paddings.LeftM,
        ...paddings.RightS,
        ...paddings.VerticalS,
        borderWidth: bordersComponent.borderWidth,
      },
      tagText: {
        ...fontSizes.small,
        ...margins.RightXS,
      },
      fileAttachmentStyle: {
        ...bordersComponent,
        borderColor: theme.colors.border,
        ...paddings.S,
        ...paddings.RightXL,
        backgroundColor: theme.colors.surface,
      },
      fileAttachmentButtonStyle: {
        backgroundColor: theme.colors.accent,
        ...bordersComponent,
        borderWidth: 0,
        justifyContent: 'center',
        alignItems: 'center',
        ...paddings.VerticalL,
        ...paddings.HorizontalXL,
      },
      dataTableStyle: {
        borderBottomWidth: bordersComponent.borderWidth,
        borderColor: theme.colors.border,
        flex: 1,
        alignItems: 'stretch',
      },
      dataTableRowStyle: {
        flexDirection: 'row',
        borderLeftWidth: 0,
        borderRightWidth: 0,
      },
      dataTableCellStyle: {
        justifyContent: 'center',
        flexDirection: 'row',
        alignItems: 'center',
        ...paddings.HorizontalS,
        ...paddings.VerticalML,
        borderRightWidth: bordersComponent.borderWidth,
        borderColor: theme.colors.border,
        overflow: 'hidden',
      },
      dataTableHeaderCellStyle: {
        justifyContent: 'center',
        flexDirection: 'row',
        alignItems: 'center',
        ...paddings.HorizontalS,
        ...paddings.VerticalM,
        borderColor: 'rgba(255, 255, 255, 1)',
        borderRightWidth: 0,
        backgroundColor: theme.colors.surface,
        lineHeight: 22,
      },
      dataTableHeaderTextStyle: {
        ...fonts.medium,
        ...fontSizes.xSmall,
        textAlign: 'left',
        lineHeight: 15,
        letterSpacing: 0,
        color: theme.colors.text,
      },
      dataTableHeaderRowStyle: {
        borderBottomWidth: 2,
        borderColor: theme.colors.secondary,
      },
      iconContainerStyle: {
        borderRadius: bordersComponent.borderRadius,
      },
      iconContainerInvertedStyle: {
        borderRadius: bordersComponent.borderRadius,
        ...paddings.S,
      },
      iconStyle: {},
      linkedItemStyle: {
        ...bordersComponent,
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        borderColor: theme.colors.border,
        ...paddings.S,
        backgroundColor: theme.colors.surface,
        maxWidth: AppStyles.defaultValues.largeComponentWidth,
      },
      linkedItemCompactStyle: {
        ...bordersComponent,
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        borderColor: theme.colors.border,
        ...paddings.S,
        backgroundColor: theme.colors.surface,
        maxWidth: AppStyles.defaultValues.defaultComponentWidth,
      },
      linkedItemIconStyle: {
        ...bordersComponent,
        borderWidth: 1,
        borderColor: theme.colors.border,
        ...paddings.S,
      },
      linkedItemTextStyle: {
        ...fonts.regular,
        ...fontSizes.small,
        ...margins.RightML,
      },
      clickableTextStyle: {
        color: theme.colors.buttonPrimary,
        ...fonts.medium,
        ...fontSizes.mediumSmall,
        letterSpacing: 1.2,
      },
      smallClickableTextStyle: {
        color: theme.colors.buttonPrimary,
        ...fonts.medium,
        ...fontSizes.xxSmall,
        letterSpacing: 0.2,
      },
      textStyle: {
        color: theme.colors.text,
        lineHeight: 25,
        ...fonts.regular,
        ...fontSizes.small,
      },
      inputSetStyle: {
        maxWidth: AppStyles.defaultValues.largeComponentWidth,
      },
      inputSetItemStyle: {
        ...margins.S,
        ...paddings.TopM,
        ...paddings.LeftML,
        ...paddings.BottomM,
        ...paddings.RightS,
        ...bordersComponent,
        borderColor: theme.colors.border,
        backgroundColor: theme.colors.surface,
      },
      tabPanelStyle: {
        flexDirection: 'column',
        alignItems: 'stretch',
        flex: 1,
        ...bordersPrimary,
        borderColor: theme.colors.border,
        backgroundColor: theme.colors.surface,
      },
      reorderControlsStyle: {
        ...fonts.regular,
        ...fontSizes.small,
        color: theme.colors.secondary,
      },
      messageDialogStyle: {
        width: 620,
        height: 550,
        backgroundColor: theme.colors.background,
      },
      relationshipsDialogStyle: {
        width: 485,
        height: 550,
      },
      upperCase: {
        textTransform: 'uppercase',
      },
      shadow: {
        ...shadow,
        shadowColor: theme.colors.mediumGray,
      },
      warfightingFunctionText: {
        marginLeft: 16,
      },
      warfightingDialogStyle: {
        width: 660,
        height: 770,
        backgroundColor: theme.colors.background,
      },
      warfightingFunctionListHeader: {
        ...fonts.bold,
      },
      analyticsHeaderStyle: {
        ...bordersPrimary,
        ...margins.HorizontalML,
        ...margins.TopML,
        ...paddings.VerticalM,
        ...paddings.HorizontalML,
        backgroundColor: theme.colors.surface,
        borderColor: theme.colors.border,
      },
      dataCardStyle: {
        backgroundColor: theme.colors.paper,
        borderRadius: 8,
        borderColor: theme.colors.border,
        borderWidth: 1,
        width: '100%',
        height: '100%',
        flexShrink: 1,
      },
      ...overrides?.components,
    };
    const borders: StyleSheet.NamedStyles<BordersStyle> = {
      primary: {
        ...bordersPrimary,
        borderColor: theme.colors.border,
      },
      component: {
        ...bordersComponent,
        borderColor: theme.colors.border,
      },
      None: {
        borderTopWidth: 0,
        borderBottomWidth: 0,
        borderLeftWidth: 0,
        borderRightWidth: 0,
      },
    };
    this.styles = {
      defaultValues: AppStyles.defaultValues,
      borders: StyleSheet.create(borders),
      margins: StyleSheet.create(margins),
      paddings: StyleSheet.create(paddings),
      fonts: StyleSheet.create(fonts),
      fontSizes: StyleSheet.create(fontSizes),
      components: StyleSheet.create(components),
    };
    return this.styles;
  }
}
