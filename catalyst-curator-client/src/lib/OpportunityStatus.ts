type Status = 'APPROVED' | 'PENDING' | 'ARCHIVED' | 'DELETED';
export function mapStatusToColor(theme: ReactNativePaper.ThemeProp, status: Status): string {
  const { colors } = theme;

  switch (status) {
    case 'APPROVED':
      return colors.set1Color2;
    case 'PENDING':
      return colors.set1Color3;
    case 'ARCHIVED':
      return colors.set1Color5;
    case 'DELETED':
      return colors.set1Color1;
    default:
      return colors.text;
  }
}
