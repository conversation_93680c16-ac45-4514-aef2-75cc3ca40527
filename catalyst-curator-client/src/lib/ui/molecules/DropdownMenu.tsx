import { observer } from 'mobx-react';
import React from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { MenuItem, PopupListMenu, PopupListMenuProps } from '../atoms/PopupListMenu';
import { Text } from '../atoms/Text';

export interface DropdownMenuProps extends PopupListMenuProps {
  // may be observable
  getValue?: () => MenuItem | undefined;
  anchorStyle?: StyleProp<ViewStyle>;
}

export const DropdownMenu = withTheme(
  observer(
    ({
      getValue = () => undefined,
      anchorStyle,
      ...rest
    }: DropdownMenuProps & { theme: ReactNativePaper.ThemeProp }) => {
      const {
        styles: { components, margins },
        fonts,
        colors,
      } = rest.theme;
      const { getEditable } = rest;
      const editable = getEditable && getEditable();
      return (
        <PopupListMenu
          {...rest}
          anchor={
            <View
              style={[
                editable ? components.dropDownMenuAnchorStyle : components.dropDownMenuReadOnlyAnchorStyle,
                {
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                },
                anchorStyle,
              ]}
            >
              <Text style={[components.dropDownMenuAnchorTextStyle]} numberOfLines={1}>
                {getValue()?.label || (editable ? 'Select...' : 'No Selection')}
              </Text>
              {editable && <Text style={[margins.LeftL, { color: colors.border }]}>{'\u25bc'}</Text>}
            </View>
          }
        />
      );
    },
  ),
);
