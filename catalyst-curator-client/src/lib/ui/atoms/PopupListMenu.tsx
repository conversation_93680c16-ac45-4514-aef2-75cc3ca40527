import { observable } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react';
import React, { ReactNode, useRef } from 'react';
import { Pressable, StyleProp, TextStyle, ViewStyle } from 'react-native';
import { List, Menu as RNPMenu, withTheme } from 'react-native-paper';

export type MenuItem = {
  type?: 'item' | 'heading';
  label: string;
  value: any;
};
export interface PopupListMenuProps {
  // may be observable
  getMenuItems?: () => MenuItem[] | undefined;
  getEditable?: () => boolean;
  style?: StyleProp<ViewStyle>;
  menuItemStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  anchor?: ReactNode;
  onItemSelected?: (item: MenuItem) => void;
}

export const PopupListMenu = withTheme(
  observer(
    ({
      style,
      theme,
      getMenuItems = () => undefined,
      anchor,
      textStyle,
      menuItemStyle,
      onItemSelected: onItemSelected = () => {},
      getEditable = () => true,
    }: PopupListMenuProps & { theme: ReactNativePaper.ThemeProp }) => {
      const {
        styles: { components, margins },
      } = theme;

      const localStore = useLocalObservable(() => ({
        visible: false,
        setVisible(value: boolean) {
          this.visible = value;
        },
      }));

      return (
        <RNPMenu
          visible={localStore.visible}
          onDismiss={() => localStore.setVisible(false)}
          contentStyle={[components.menuStyle, style, { overflow: 'hidden' }]}
          anchor={
            getEditable && getEditable() ? (
              <Pressable onPress={() => localStore.setVisible(true)}>{anchor}</Pressable>
            ) : (
              anchor
            )
          }
        >
          {getMenuItems()?.map((item: MenuItem, index: number) => {
            return (
              <List.Item
                contentStyle={[components.menuItemStyle, menuItemStyle]}
                key={`${index}-${item.label}`} // Not ideal to use index here.
                title={item.label}
                titleStyle={[components.menuTextStyle, textStyle]}
                onPress={() => {
                  localStore.setVisible(false);
                  onItemSelected(item);
                }}
              />
            );
          })}
        </RNPMenu>
      );
    },
  ),
);
