import { observer, useLocalObservable } from 'mobx-react';
import { View, StyleProp, ViewStyle, ScrollView, Pressable, Modal } from 'react-native';
import { Checkbox, Text, Chip, IconButton, withTheme } from 'react-native-paper';

export type MultiselectMenuItem = {
  label: string;
  value: any;
  disabled?: boolean;
};

export interface MultiselectMenuProps {
  // may be observable
  getMenuItems?: () => MultiselectMenuItem[] | undefined;
  getSelectedValues?: () => any[];
  onSelectionChange?: (selectedValues: any[]) => void;
  getEditable?: () => boolean;
  style?: StyleProp<ViewStyle>;
  placeholder?: string;
  maxHeight?: number;
  showSelectAll?: boolean;
  chipStyle?: StyleProp<ViewStyle>;
  menuStyle?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
}

export const MultiselectMenu = withTheme(
  observer(
    ({
      style,
      theme,
      getMenuItems = () => [],
      getSelectedValues = () => [],
      onSelectionChange = () => {},
      getEditable = () => true,
      placeholder = 'Select items...',
      maxHeight = 300,
      showSelectAll = true,
      chipStyle,
      menuStyle,
    }: MultiselectMenuProps) => {
      const { colors } = theme;

      const localStore = useLocalObservable(() => ({
        isMenuOpen: false,
        setIsMenuOpen(value: boolean) {
          this.isMenuOpen = value;
        },
      }));

      const menuItems = getMenuItems() || [];
      const selectedValues = getSelectedValues();
      const editable = getEditable();

      const toggleOption = (value: any) => {
        if (!editable) return;

        const newSelection = selectedValues.includes(value)
          ? selectedValues.filter((v) => v !== value)
          : [...selectedValues, value];

        onSelectionChange(newSelection);
      };

      const removeChip = (value: any) => {
        if (!editable) return;

        const newSelection = selectedValues.filter((v) => v !== value);
        onSelectionChange(newSelection);
      };

      const selectAll = () => {
        if (!editable) return;

        const allValues = menuItems.filter((item) => !item.disabled).map((item) => item.value);
        onSelectionChange(allValues);
      };

      const clearAll = () => {
        if (!editable) return;
        onSelectionChange([]);
      };

      const isAllSelected = () => {
        const enabledItems = menuItems.filter((item) => !item.disabled);
        return enabledItems.length > 0 && enabledItems.every((item) => selectedValues.includes(item.value));
      };

      const getSelectedLabels = () => {
        return menuItems
          .filter((item) => selectedValues.includes(item.value))
          .map((item) => ({ label: item.label, value: item.value }));
      };

      return (
        <View style={[{ position: 'relative' }, style]}>
          <Pressable
            onPress={() => {
              console.log('MultiselectMenu clicked, editable:', editable);
              if (editable) {
                localStore.setIsMenuOpen(true);
                console.log('Menu should open now');
              }
            }}
            style={[
              {
                flexDirection: 'row',
                alignItems: 'center',
                borderWidth: 1,
                borderColor: colors.disabled,
                borderRadius: 8,
                padding: 8,
                minHeight: 48,
                backgroundColor: editable ? colors.surface : colors.backdrop,
              },
            ]}
          >
            <View style={{ flex: 1 }}>
              {selectedValues.length > 0 ? (
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={{ flexDirection: 'row', gap: 4 }}>
                    {getSelectedLabels().map((item) => (
                      <Chip
                        key={item.value}
                        onClose={editable ? () => removeChip(item.value) : undefined}
                        style={[{ marginRight: 4 }, chipStyle]}
                        textStyle={{ fontSize: 12 }}
                      >
                        {item.label}
                      </Chip>
                    ))}
                  </View>
                </ScrollView>
              ) : (
                <Text style={{ color: colors.placeholder, fontSize: 16 }}>{placeholder}</Text>
              )}
            </View>
            {editable && (
              <IconButton
                icon={localStore.isMenuOpen ? 'chevron-up' : 'chevron-down'}
                size={20}
                onPress={() => localStore.setIsMenuOpen(!localStore.isMenuOpen)}
              />
            )}
          </Pressable>

          <Modal
            visible={localStore.isMenuOpen}
            transparent={true}
            animationType="fade"
            onRequestClose={() => localStore.setIsMenuOpen(false)}
          >
            <Pressable
              style={{
                flex: 1,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={() => localStore.setIsMenuOpen(false)}
            >
              <Pressable
                style={[
                  {
                    backgroundColor: colors.surface,
                    borderRadius: 8,
                    padding: 16,
                    maxWidth: 400,
                    width: '90%',
                    maxHeight: maxHeight,
                  },
                  menuStyle,
                ]}
                onPress={(e) => e.stopPropagation()}
              >
                <ScrollView>
                  {showSelectAll && menuItems.length > 0 && (
                    <>
                      <Pressable
                        onPress={isAllSelected() ? clearAll : selectAll}
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          paddingVertical: 8,
                        }}
                      >
                        <Checkbox status={isAllSelected() ? 'checked' : 'unchecked'} />
                        <Text style={{ fontWeight: 'bold', marginLeft: 8 }}>
                          {isAllSelected() ? 'Clear All' : 'Select All'}
                        </Text>
                      </Pressable>
                      <View
                        style={{
                          height: 1,
                          backgroundColor: colors.disabled,
                          marginVertical: 8,
                        }}
                      />
                    </>
                  )}
                  {menuItems.map((item, index) => (
                    <Pressable
                      key={`${index}-${item.value}`}
                      onPress={() => !item.disabled && toggleOption(item.value)}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: 8,
                        opacity: item.disabled ? 0.5 : 1,
                      }}
                      disabled={item.disabled}
                    >
                      <Checkbox
                        status={selectedValues.includes(item.value) ? 'checked' : 'unchecked'}
                        disabled={item.disabled}
                      />
                      <Text style={{ marginLeft: 8, flex: 1 }}>{item.label}</Text>
                    </Pressable>
                  ))}
                  {menuItems.length === 0 && (
                    <Text style={{ textAlign: 'center', color: colors.placeholder, padding: 16 }}>
                      No items available
                    </Text>
                  )}
                </ScrollView>
              </Pressable>
            </Pressable>
          </Modal>
        </View>
      );
    },
  ),
);
