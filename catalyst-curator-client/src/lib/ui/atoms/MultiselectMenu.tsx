import React from 'react';
import { observer, useLocalObservable } from 'mobx-react';
import { View, StyleProp, ViewStyle, ScrollView, Pressable } from 'react-native';
import { Checkbox, Text, withTheme, Menu } from 'react-native-paper';
import { Icon } from './Icon';
import { IconButton } from './IconButton';
import { AppStyles } from '../../style';

export type MultiselectMenuItem = {
  label: string;
  value: any;
  disabled?: boolean;
  exclusive?: boolean; // If true, this item cannot be selected with others
  separatorAfter?: boolean; // If true, shows a separator line after this item
};

export interface MultiselectMenuProps {
  // may be observable
  getMenuItems?: () => MultiselectMenuItem[] | undefined;
  getSelectedValues?: () => any[];
  onSelectionChange?: (selectedValues: any[]) => void;
  getEditable?: () => boolean;
  style?: StyleProp<ViewStyle>;
  placeholder?: string;
  maxHeight?: number;
  showSelectAll?: boolean;
  chipStyle?: StyleProp<ViewStyle>;
  menuStyle?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
}

export const MultiselectMenu = withTheme(
  observer(
    ({
      style,
      theme,
      getMenuItems = () => [],
      getSelectedValues = () => [],
      onSelectionChange = () => {},
      getEditable = () => true,
      placeholder = 'Select items...',
      maxHeight = 300,
      showSelectAll = true,
      chipStyle,
      menuStyle,
    }: MultiselectMenuProps) => {
      const {
        colors,
        styles: { components },
      } = theme;

      const localStore = useLocalObservable(() => ({
        isMenuOpen: false,
        setIsMenuOpen(value: boolean) {
          this.isMenuOpen = value;
        },
      }));

      const menuItems = getMenuItems() || [];
      const selectedValues = getSelectedValues();
      const editable = getEditable();

      const toggleOption = (value: any) => {
        if (!editable) return;

        const clickedItem = menuItems.find((item) => item.value === value);
        const isCurrentlySelected = selectedValues.includes(value);

        if (isCurrentlySelected) {
          // If deselecting, just remove it
          const newSelection = selectedValues.filter((v) => v !== value);
          onSelectionChange(newSelection);
        } else {
          // If selecting, check for exclusive logic
          if (clickedItem?.exclusive) {
            // If this is an exclusive item, clear all others and select only this
            onSelectionChange([value]);
          } else {
            // If this is a regular item, check if any exclusive items are selected
            const hasExclusiveSelected = selectedValues.some(
              (selectedValue) => menuItems.find((item) => item.value === selectedValue)?.exclusive,
            );

            if (hasExclusiveSelected) {
              // If an exclusive item is selected, replace it with this regular item
              onSelectionChange([value]);
            } else {
              // Normal multi-select behavior
              onSelectionChange([...selectedValues, value]);
            }
          }
        }
      };

      const removeChip = (value: any) => {
        if (!editable) return;

        const newSelection = selectedValues.filter((v) => v !== value);
        onSelectionChange(newSelection);
      };

      const selectAll = () => {
        if (!editable) return;

        // Only select non-exclusive, non-disabled items for "Select All"
        const allValues = menuItems.filter((item) => !item.disabled && !item.exclusive).map((item) => item.value);
        onSelectionChange(allValues);
      };

      const clearAll = () => {
        if (!editable) return;
        onSelectionChange([]);
      };

      const isAllSelected = () => {
        const enabledNonExclusiveItems = menuItems.filter((item) => !item.disabled && !item.exclusive);
        return (
          enabledNonExclusiveItems.length > 0 &&
          enabledNonExclusiveItems.every((item) => selectedValues.includes(item.value))
        );
      };

      const getSelectedLabels = () => {
        return menuItems
          .filter((item) => selectedValues.includes(item.value))
          .map((item) => ({ label: item.label, value: item.value }));
      };

      return (
        <Menu
          visible={localStore.isMenuOpen}
          onDismiss={() => localStore.setIsMenuOpen(false)}
          anchorPosition="bottom"
          contentStyle={[
            {
              maxHeight: maxHeight,
              maxWidth: AppStyles.defaultValues.largeComponentWidth,
              width: '100%',
              overflow: 'scroll',
              elevation: 0,
              backgroundColor: theme.colors.light,
            },
            menuStyle,
          ]}
          anchor={
            <Pressable
              onPress={() => {
                if (editable) {
                  localStore.setIsMenuOpen(true);
                }
              }}
              style={[
                editable ? components.dropDownMenuAnchorStyle : components.dropDownMenuReadOnlyAnchorStyle,
                {
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                },
                style,
              ]}
            >
              <View style={[{ flex: 1 }]}>
                {selectedValues.length > 0 ? (
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    <View style={{ flexDirection: 'row', gap: 4 }}>
                      {getSelectedLabels().map((item) => (
                        <View
                          style={[
                            {
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              gap: 4,
                              backgroundColor: '#D8E2F8',
                              paddingHorizontal: 8,
                              paddingVertical: 4,
                              borderRadius: 4,
                            },
                            chipStyle,
                          ]}
                        >
                          <Text>{item.label}</Text>
                          <IconButton icon="close" onPress={() => removeChip(item.value)} size={16} />
                        </View>
                      ))}
                    </View>
                  </ScrollView>
                ) : (
                  <Text style={{ fontSize: 16 }}>{placeholder}</Text>
                )}
              </View>
              {editable && <Text style={[{ color: colors.border }]}>{'\u25bc'}</Text>}
            </Pressable>
          }
        >
          <ScrollView>
            {showSelectAll && menuItems.length > 0 && (
              <>
                <Pressable
                  onPress={isAllSelected() ? clearAll : selectAll}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                >
                  <Checkbox status={isAllSelected() ? 'checked' : 'unchecked'} />
                  <Text style={{ fontWeight: 'bold', marginLeft: 8 }}>
                    {isAllSelected() ? 'Clear All' : 'Select All'}
                  </Text>
                </Pressable>
                <View
                  style={{
                    height: 1,
                    backgroundColor: colors.disabled,
                    marginVertical: 8,
                  }}
                />
              </>
            )}
            {menuItems.map((item, index) => (
              <React.Fragment key={`${index}-${item.value}`}>
                <Pressable
                  onPress={() => !item.disabled && toggleOption(item.value)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    opacity: item.disabled ? 0.5 : 1,
                  }}
                  disabled={item.disabled}
                >
                  <Checkbox
                    status={selectedValues.includes(item.value) ? 'checked' : 'unchecked'}
                    disabled={item.disabled}
                  />
                  <Text style={{ marginLeft: 8, flex: 1 }}>{item.label}</Text>
                </Pressable>
                {item.separatorAfter && (
                  <View
                    style={{
                      height: 1,
                      backgroundColor: colors.disabled,
                      marginVertical: 8,
                    }}
                  />
                )}
              </React.Fragment>
            ))}
            {menuItems.length === 0 && (
              <Text style={{ textAlign: 'center', color: colors.placeholder, padding: 16 }}>No items available</Text>
            )}
          </ScrollView>
        </Menu>
      );
    },
  ),
);
