import {
  UploadAttachmentDocument,
  Attachment,
  GetAttachmentLocationDocument,
  DeleteAttachmentDocument,
  Location,
  UploadAttachmentMutationVariables,
  UploadAttachmentMutation,
  MutationAddAttachmentArgs,
  GetAttachmentLocationQuery,
  GetAttachmentLocationQueryVariables,
  QueryGetAttachmentLocationArgs,
  MutationDeleteAttachmentArgs,
  DeleteAttachmentMutation,
  DeleteAttachmentMutationVariables,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class AttachmentService {
  async uploadAttachment(input: MutationAddAttachmentArgs): Promise<Attachment> {
    const { data, error } = await urqlClient.executeMultipartExchangeMutation<
      UploadAttachmentMutation,
      UploadAttachmentMutationVariables
    >(UploadAttachmentDocument, input);
    if (error) throw error;
    return data!.addAttachment as Attachment;
  }

  async getAttachment(input: QueryGetAttachmentLocationArgs): Promise<Location> {
    const { data, error } = await urqlClient.executeQuery<
      GetAttachmentLocationQuery,
      GetAttachmentLocationQueryVariables
    >(GetAttachmentLocationDocument, input);
    if (error) throw error;
    return data!.getAttachmentLocation as Location;
  }

  async deleteAttachment(input: MutationDeleteAttachmentArgs): Promise<boolean> {
    const { data, error } = await urqlClient.executeMutation<
      DeleteAttachmentMutation,
      DeleteAttachmentMutationVariables
    >(DeleteAttachmentDocument, input);
    if (error) throw error;
    return data!.deleteAttachment;
  }
}

export const attachmentService = new AttachmentService();
