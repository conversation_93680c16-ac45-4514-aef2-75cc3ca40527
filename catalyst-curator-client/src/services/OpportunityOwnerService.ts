import { OperationResult } from '@urql/core';
import {
  AssignOpportunityOwnerInput,
  AssignOwnerDocument,
  OpportunityOwner,
  QuerySearchOwnersArgs,
  SearchOwnerPage,
  SearchOwnersDocument,
  SearchOwnersQuery,
  SearchOwnersQueryVariables,
  ToggleRemovedOwnerDocument,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class OpportunityOwnerService {
  async addOwner(input: AssignOpportunityOwnerInput): Promise<OpportunityOwner> {
    return urqlClient.executeMutation(AssignOwnerDocument, { input }).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<OpportunityOwner>((resolve, reject) => {
        if (data) {
          return resolve(data.assignOwner);
        }
        return reject(error);
      });
    });
  }

  async searchOwners(input: QuerySearchOwnersArgs) {
    const { data, error } = await urqlClient.executeQueryNoCache<SearchOwnersQuery, SearchOwnersQueryVariables>(
      SearchOwnersDocument,
      input,
    );
    if (error) throw error;
    return data!.searchOwners as SearchOwnerPage;
  }

  async toggleRemovedOwner(id: string): Promise<OpportunityOwner> {
    return urqlClient.executeMutation(ToggleRemovedOwnerDocument, { id }).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<OpportunityOwner>((resolve, reject) => {
        if (data) {
          return resolve(data.removeOwner);
        }
        return reject(error);
      });
    });
  }
}

export const opportunityOwnerService = new OpportunityOwnerService();
