import {
  CreateExistingSolutionDocument,
  CreateExistingSolutionMutation,
  CreateExistingSolutionMutationVariables,
  DeleteExistingSolutionDocument,
  DeleteExistingSolutionMutation,
  DeleteExistingSolutionMutationVariables,
  ExistingSolution,
  GetExistingSolutionDocument,
  GetExistingSolutionQuery,
  GetExistingSolutionQueryVariables,
  GetExistingSolutionsByOpportunityDocument,
  GetExistingSolutionsByOpportunityQuery,
  GetExistingSolutionsByOpportunityQueryVariables,
  MutationCreateExistingSolutionArgs,
  MutationDeleteExistingSolutionArgs,
  MutationUpdateExistingSolutionArgs,
  QueryGetExistingSolutionArgs,
  QueryGetExistingSolutionsByOpportunityArgs,
  UpdateExistingSolutionDocument,
  UpdateExistingSolutionMutation,
  UpdateExistingSolutionMutationVariables,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class ExistingSolutionService {
  async createExistingSolution(input: MutationCreateExistingSolutionArgs): Promise<ExistingSolution> {
    const { data, error } = await urqlClient.executeMutation<
      CreateExistingSolutionMutation,
      CreateExistingSolutionMutationVariables
    >(CreateExistingSolutionDocument, input);
    if (error) throw error;
    return data!.createExistingSolution as ExistingSolution;
  }

  async getExistingSolution(input: QueryGetExistingSolutionArgs): Promise<ExistingSolution> {
    const { data, error } = await urqlClient.executeQuery<
      GetExistingSolutionQuery,
      GetExistingSolutionQueryVariables
    >(GetExistingSolutionDocument, input);
    if (error) throw error;
    return data!.getExistingSolution as ExistingSolution;
  }

  async getExistingSolutionsByOpportunity(
    input: QueryGetExistingSolutionsByOpportunityArgs,
  ): Promise<ExistingSolution[]> {
    const { data, error } = await urqlClient.executeQuery<
      GetExistingSolutionsByOpportunityQuery,
      GetExistingSolutionsByOpportunityQueryVariables
    >(GetExistingSolutionsByOpportunityDocument, input);
    if (error) throw error;
    return data!.getExistingSolutionsByOpportunity as ExistingSolution[];
  }

  async updateExistingSolution(input: MutationUpdateExistingSolutionArgs): Promise<ExistingSolution> {
    const { data, error } = await urqlClient.executeMutation<
      UpdateExistingSolutionMutation,
      UpdateExistingSolutionMutationVariables
    >(UpdateExistingSolutionDocument, input);
    if (error) throw error;
    return data!.updateExistingSolution as ExistingSolution;
  }

  async deleteExistingSolution(input: MutationDeleteExistingSolutionArgs): Promise<boolean> {
    const { data, error } = await urqlClient.executeMutation<
      DeleteExistingSolutionMutation,
      DeleteExistingSolutionMutationVariables
    >(DeleteExistingSolutionDocument, input);
    if (error) throw error;
    return data!.deleteExistingSolution;
  }
}

export const existingSolutionService = new ExistingSolutionService();
