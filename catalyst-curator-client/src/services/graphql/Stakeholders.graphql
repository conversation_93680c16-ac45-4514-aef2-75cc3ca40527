mutation createStakeholder($input: CreateStakeholderInput!) {
  createStakeholder(input: $input) {
    id
    name
    org
  }
}

query getStakeholder($id: String, $name: String, $org: String) {
  getStakeholder(name: $name, id: $id, org: $org) {
    id
    name
    org
  }
}

query queryStakeholders($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  queryStakeholders(pagingInput: $pagingInput, searchSortInput: $searchSortInput) {
    results {
      id
      name
      org
    }
  }
}
