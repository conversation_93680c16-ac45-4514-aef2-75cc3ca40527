query getProject($id: String!) {
  getProject(id: $id) {
    id
    createdAt
    lastCurated
    title
    summary
    status
    background
    startDate
    endDate
    goals
    type
    otherType
    statusNotes
    opportunities {
      id
      title
      stakeholders {
        id
        name
        org
      }
    }
    categories {
      id
      name
    }
    projectStakeholders {
      id
      type
      stakeholder {
        id
        name
        org
      }
    }
    creator {
      id
      createdAt
      emailAddress
      firstName
      lastName
    }
    attachments {
      id
      createdAt
      updatedAt
      name
      encoding
      mimetype
    }
    curationInfo {
      users {
        id
        firstName
        lastName
      }
      lastCurated
    }
  }
}

mutation createProjectFromOpportunity($input: CreateProjectFromOpportunityInput!) {
  createProjectFromOpportunity(input: $input) {
    id
    createdAt
    lastCurated
    title
    summary
    status
    background
    startDate
    endDate
    goals
    type
    otherType
    statusNotes
    opportunities {
      id
      title
    }
    categories {
      id
      name
    }
    projectStakeholders {
      id
      type
      stakeholder {
        id
        name
        org
      }
    }
    creator {
      id
      createdAt
      emailAddress
      firstName
      lastName
    }
    attachments {
      id
      createdAt
      updatedAt
      name
      encoding
      mimetype
    }
    curationInfo {
      users {
        id
        firstName
        lastName
      }
      lastCurated
    }
  }
}

mutation updateProject($input: UpdateProjectInput!, $id: String!, $links: UpdateProjectLinks) {
  updateProject(input: $input, links: $links, id: $id) {
    lastCurated
    curationInfo {
      users {
        id
        firstName
        lastName
      }
      lastCurated
    }
  }
}

query queryProjects($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  queryProjects(pagingInput: $pagingInput, searchSortInput: $searchSortInput) {
    results {
      id
      createdAt
      lastCurated
      title
      status
      type
      startDate
      endDate
      otherType
      statusNotes
      projectStakeholders {
        id
        type
        stakeholder {
          id
          name
          org
        }
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}

mutation deleteProject($id: String!) {
  deleteProject(id: $id)
}
