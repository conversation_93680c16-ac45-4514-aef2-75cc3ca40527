mutation assignOwner($input: AssignOpportunityOwnerInput!) {
  assignOwner(input: $input) {
    owner {
      emailAddress
      firstName
      lastName
      org1
      org2
      org3
      org4
      phone
      altContact
      organizationRole
    }
    id
    addedAt
    removedAt
    isRemoved
    madePreviousAt
    status
  }
}

query searchOwners($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  searchOwners(pagingInput: $pagingInput, searchSortInput: $searchSortInput) {
    results {
      id
      emailAddress
      firstName
      lastName
      phone
      org1
      org2
      org3
      org4
      source
      organizationRole
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}

mutation toggleRemovedOwner($id: String!) {
  toggleRemovedOwner(id: $id) {
    id
  }
}
