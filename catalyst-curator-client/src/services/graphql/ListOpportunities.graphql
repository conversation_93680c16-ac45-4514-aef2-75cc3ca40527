query filterOpportunities($searchSortInput: SearchSortInput, $pagingInput: PagingInput, $scope: Scope) {
  queryOpportunities(searchSortInput: $searchSortInput, pagingInput: $pagingInput, scope: $scope) {
    results {
      id
      priority
      status
      function
      title
      solutionPathway
      statusNotes
      lastCurated
      createdAt
      campaign
      armyModernizationPriority
      echelonApplicability
      owners {
        id
        owner {
          emailAddress
          firstName
          lastName
          org1
          org2
          org3
          org4
          phone
          altContact
          organizationRole
        }
        id
        addedAt
        removedAt
        status
      }
      operationalRules
      transitionInContactLineOfEffort
      isTiCLOE
      capabilityArea
      org1
      org2
      org3
      org4
      relatedOpportunityCount
      parentOpportunityCount
      childOpportunityCount
      linkedOpportunityCount
      visibility
      user {
        id
        org1
        org2
        org3
        org4
      }
      categories {
        id
        name
      }
      stakeholders {
        id
        name
      }
      curationInfo {
        users {
          id
          firstName
          lastName
        }
        lastCurated
      }
      tenant {
        id
        name
        label
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}

query findOpportunities($searchSortInput: SearchSortInput, $pagingInput: PagingInput, $scope: Scope) {
  queryOpportunities(searchSortInput: $searchSortInput, pagingInput: $pagingInput, scope: $scope) {
    results {
      id
      status
      title
      ownedOpportunities {
        id
        type
        target {
          id
          title
        }
      }
      owningOpportunities {
        id
        type
        source {
          id
          title
        }
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}
