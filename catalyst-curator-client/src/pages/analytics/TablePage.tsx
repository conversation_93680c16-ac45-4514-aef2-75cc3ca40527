import { Text, View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { OpportunityListStore, OpportunityReportsStore, TenantStore, UserStore } from '../../stores';
import { MainStackParamList } from '../../routing/screens';
import { Router } from '../../platform/Router';
import { Dates, Tooltip } from '../../lib';
import { HiddenView } from '../../lib/ui/atoms/HiddenView';
import { ClickableText } from '../../lib/ui/atoms/ClickableText';
import { ApplicationMetaStore } from '../../stores/ApplicationMetaStore';
import { Opportunity, OpportunityVisibility, SearchOperator } from '../../services/codegen/types';
import { ColDef, ValueGetterParams } from 'ag-grid-community';
import { Priority } from '../../lib/Priority';
import { Icon } from '../../lib/ui/atoms/Icon';
import { IconLabel } from '../../lib/ui/atoms/IconLabel';
import { GridControl } from '../../appComponents/web/Grid.web';
import { GenericGrid } from '../../appComponents/GenericGrid';
import { PRIORITY_LABELS } from '../../lib/ui/organisms/PriorityMenu';
import { FilterInfoStore } from '../../stores/FilterInfoStore';
import { Checkbox } from '../../lib/ui/atoms/Checkbox';
import { useLocalObservable } from 'mobx-react';
import { AnalyticsHeader } from '../../appComponents/header/AnalyticsHeader';
import getHeaderConfig from '../../routing/analytics/getHeaderConfig';
import { AnalyticsStackParamList } from '../../routing/analytics/screens';
import { Route } from '@react-navigation/native';
import { ResourcesStore } from '../../stores/ResourcesStore';
import { ArmyModernizationPrioritiesItems } from '../sections/opportunity/curated-opportunity-groups/ArmyModernizationPriorities';

interface TablePageProps {
  opportunityListStore: OpportunityListStore;
  mainStackRouter: Router<MainStackParamList>;
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  tenantStore: TenantStore;
  eventFilterInfo: FilterInfoStore;
  router: Router<AnalyticsStackParamList>;
  resourcesStore: ResourcesStore;
  opportunityReportsStore: OpportunityReportsStore;
  route: Route<string>;
}

export const TablePage = withTheme(
  ({
    theme,
    opportunityListStore,
    mainStackRouter,
    userStore,
    tenantStore,
    eventFilterInfo,
    route,
    router,
    opportunityReportsStore,
    resourcesStore,
  }: TablePageProps) => {
    const {
      styles: { components, margins },
    } = theme;
    const { userMetaStore } = userStore;

    const localStore = useLocalObservable(() => ({
      gridControl: {} as GridControl,
      setGridControl(value: GridControl) {
        this.gridControl = value;
      },
    }));

    const colDefsMap = getColDefsMap(theme, tenantStore, opportunityListStore);

    const appHeaderProps = getHeaderConfig(route.name as keyof AnalyticsStackParamList);

    function setEventFilterInfo() {
      const filterStores = opportunityListStore.listFilterStores;
      const eventsCheckboxes = filterStores.campaignInfoFilterStore.info;
      if (location.pathname.includes('overview')) {
        eventsCheckboxes.splice(0, 1);
        const noneIndex = eventsCheckboxes.findIndex((info) => info.label === 'None');
        if (noneIndex !== -1) {
          eventsCheckboxes.splice(noneIndex, 1);
        }
      }
      return eventsCheckboxes;
    }

    return (
      <View style={[components.flexAll, margins.BottomL]}>
        <AnalyticsHeader
          {...appHeaderProps}
          router={router}
          userStore={userStore}
          resourcesStore={resourcesStore}
          opportunityReportsStore={opportunityReportsStore}
          tenantStore={tenantStore}
          setEventFilterInfo={setEventFilterInfo}
        />
        <View style={[components.flexAll, components.panelStyle, components.shadow]}>
          <View style={[{ flexDirection: 'row', alignSelf: 'flex-end' }]}>
            <HiddenView
              getVisible={() =>
                opportunityListStore.searchGroups.asArray().length !== 0 ||
                !opportunityListStore.searchFields.isDefault()
              }
            >
              <ClickableText
                style={[components.smallClickableTextStyle, margins.RightML]}
                onPress={() => handleResetFilters(opportunityListStore)}
              >
                RESET FILTERS
              </ClickableText>
            </HiddenView>
            <HiddenView getVisible={() => !!userMetaStore.anaTable?.hasHidden}>
              <ClickableText
                style={[components.smallClickableTextStyle, margins.RightML]}
                onPress={() => handleRestoreHidden(userStore, userMetaStore, localStore)}
              >
                SHOW HIDDEN
              </ClickableText>
            </HiddenView>
            <ClickableText style={[components.smallClickableTextStyle]} onPress={() => handleResetTable(userStore)}>
              RESET TABLE
            </ClickableText>
          </View>
          <GenericGrid<Opportunity>
            theme={theme}
            store={opportunityListStore}
            router={mainStackRouter}
            userStore={userStore}
            colDefsMap={colDefsMap}
            sortableCols={sortableCols}
            onRowPress={() => {}}
            getGridControl={() => localStore.gridControl}
            onGridReady={(gridControl: GridControl) => {
              localStore.gridControl = gridControl;
            }}
            table={userMetaStore.anaTable}
          />
        </View>
      </View>
    );
  },
);

const handleResetTable = (userStore: UserStore) => {
  userStore.resetAnaTableMeta();
};

const handleResetFilters = (store: OpportunityListStore) => {
  store.resetSearchValues();
  store.queryItems();
};

const handleRestoreHidden = (userStore: UserStore, userMetaStore: ApplicationMetaStore, locals: any) => {
  userMetaStore.anaTable?.showAllColumns();
  locals.gridControl?.showAllColumns();
  userStore.saveUserApplicationMeta();
};

const sortableCols = [
  'priority',
  'status',
  'title',
  'lastCurated',
  'createdAt',
  'org1',
  'solutionPathway',
  'statusNotes',
  'campaign',
  'armyModernizationPriority',
  'echelonApplicability',
  'transitionInContactLineOfEffort',
  'operationalRules',
  'capabilityArea',
  'relatedOpportunityCount',
  'tenant.label',
  'function',
  'visibility',
];


export interface GridColDef extends ColDef {}

// Note the width values here are defaults and are overridden by the defaults in tenantStore or the tenant's metadata (or the user's metadata)
const getColDefsMap = (
  theme: ReactNativePaper.ThemeProp,
  tenantStore: TenantStore,
  opportunityListStore: OpportunityListStore,
): {
  [key: string]: ColDef;
} => {
  const filterStores = opportunityListStore.listFilterStores;
  const {
    warFightingFunctionFilterStore,
    campaignInfoFilterStore,
    priorityFilterInfo,
    statusFilterInfo,
    relationshipsFilterInfo,
  } = filterStores;
  const warFightingFunctionFilterInfo = warFightingFunctionFilterStore;
  warFightingFunctionFilterInfo.info = tenantStore.tenantConfig.fields?.opportunity.function?.values
    ? [
        {
          label: 'Unassigned',
          searchField: { fieldNames: ['function'], searchValue: null, operator: SearchOperator.Eq },
        },
        ...tenantStore.tenantConfig.fields?.opportunity.function.values?.map((functionVal) => ({
          label: functionVal.label,
          searchField: { fieldNames: ['function'], searchValue: functionVal.label, operator: SearchOperator.Match },
        })),
      ]
    : [];

  const {
    fontSizes,
    colors,
    styles: { fonts, components, margins, paddings },
  } = theme;
  return {
    tenant: {
      field: 'tenant',
      headerName: 'Portfolio',
      headerComponentParams: {
        sortPath: 'label',
      },
      autoHeight: true,
      cellRenderer: (params: any) => {
        return <Text style={[fonts.medium, { textAlign: 'center' }]}>{params.data.tenant.label}</Text>;
      },
    },
    priority: {
      field: 'priority',
      headerName: 'Priority',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const priority = params.data?.priority || Priority.NONE;
        return Priority[priority].toLocaleLowerCase();
      },
      cellStyle: { textTransform: 'capitalize' },
      headerComponentParams: {
        filterInfo: priorityFilterInfo,
      },
    },
    status: {
      field: 'status',
      headerName: 'Status',
      cellRenderer: (params: any) => {
        const status = params.data?.status;
        return (
          <View
            style={
              status === 'APPROVED'
                ? [{ borderRadius: 4, borderWidth: 2, maxWidth: 86 }, paddings.HorizontalMS, paddings.VerticalS]
                : []
            }
          >
            <Text style={[fonts.medium]}>{status}</Text>
          </View>
        );
      },
      headerComponentParams: {
        filterInfo: statusFilterInfo,
      },
    },
    relatedOpportunityCount: {
      field: 'relatedOpportunityCount',
      headerName: 'Relationships',
      headerTooltip: 'Relationships',
      headerComponentParams: {
        headerRenderer: () => (
          <View>
            <Icon name="file-tree" size={23} style={[{ alignSelf: 'center' }]} />
          </View>
        ),
        filterInfo: relationshipsFilterInfo,
      },
      cellStyle: { alignItems: 'flex-start', display: 'flex', justifyContent: 'center' },
      cellRenderer: (params: any) => {
        const { relatedOpportunityCount } = params.data;

        return (
          <IconLabel
            style={[
              components.buttonSecondaryStyle,
              margins.None,
              paddings.RightMS,
              paddings.VerticalS,
              { width: 50, backgroundColor: '#E2E2E2', borderColor: '#E2E2E2' },
            ]}
            textStyle={[components.buttonSecondaryTextStyle, { color: theme.colors.text }]}
          >
            {relatedOpportunityCount}
          </IconLabel>
        );
      },
      onCellClicked: () => {},
      autoHeight: true,
    },
    title: {
      field: 'title',
      headerName: 'Problem Title',
      autoHeight: true,
      wrapText: true,
    },
    function: {
      field: 'function',
      headerName: tenantStore.tenantConfig.fields?.opportunity.function?.fieldLabel,
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: warFightingFunctionFilterInfo,
      },
    },
    visibility: {
      field: 'visibility',
      headerName: 'Private',
      cellStyle: { display: 'flex', justifyContent: 'center', alignItems: 'flex-start' },
      onCellClicked: () => {},
      cellRenderer: (params: any) => {
        return (
          <Checkbox
            getChecked={() => params.data?.visibility === OpportunityVisibility.Private}
            onChecked={() => {}}
            onUnchecked={() => {}}
            getDisabled={() => true}
            label={null}
            style={[{ alignSelf: 'center' }]}
          />
        );
      },
      headerComponentParams: {
        headerRenderer: () => (
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'flex-start',
              gap: 4,
            }}
          >
            <Text style={[components.dataTableHeaderTextStyle, fontSizes.small, { alignSelf: 'center' }]}>Private</Text>
            <Tooltip
              offsetY={-329}
              useTopPointer={true}
              text={`By selecting the Private button, you opt to keep the submission and related opportunity data at your unit level for further review and development. \nYour higher headquarters and other Army organizations will be unable to view the submission and opportunity until you opt out of Private mode.`}
            >
              <Icon name="help-circle" color={colors.textSecondary} size={20} />
            </Tooltip>
          </View>
        ),
      },
    },
    campaign: {
      field: 'campaign',
      wrapText: true,
      headerName: tenantStore.tenantConfig.fields?.opportunity?.campaign?.fieldLabel
        ? tenantStore.tenantConfig.fields.opportunity.campaign.fieldLabel
        : 'Event',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return params.data?.campaign;
      },
      headerComponentParams: {
        filterInfo: campaignInfoFilterStore,
      },
      autoHeight: true,
    },
    lastCurated: {
      field: 'lastCurated',
      headerName: 'Last Curated',
      cellRenderer: (params: any) => {
        if (!params.data?.curationInfo?.lastCurated) {
          return (
            <View style={{ justifyContent: 'center', alignItems: 'center', display: 'flex' }}>
              <View
                style={[
                  components.auxButton3Style,
                  components.buttonCompactStyle,
                  margins.None,
                  { width: 70, backgroundColor: '#676D79', borderWidth: 0, alignItems: 'center' },
                ]}
              >
                <Text style={[components.auxButton3TextStyle, components.buttonCompactStyle, { borderWidth: 0 }]}>
                  New
                </Text>
              </View>
            </View>
          );
        }

        return Dates.asSimpleDateString(params.data?.lastCurated);
      },
    },
    createdAt: {
      field: 'createdAt',
      headerName: 'Created',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return Dates.asSimpleDateString(params.data?.createdAt);
      },
    },
    org1: {
      field: 'org1',
      headerName: 'Org / Team',
      autoHeight: true,
      wrapText: true,
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const org1 = params.data?.org1;
        const org2 = params.data?.org2;
        return org1 || org2 ? `${org1 || 'N/A'}${org2 ? ' / ' + org2 : ''}` : 'N/A';
      },
    },
    stakeholders: {
      field: 'stakeholders',
      headerName: 'Stakeholders',
      autoHeight: true,
      wrapText: true,

      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return params.data?.stakeholders.map((stakeholder) => (stakeholder.name ? `${stakeholder.name}` : ''));
      },
    },
    armyModernizationPriority: {
      field: 'armyModernizationPriority',
      headerName: 'Army Modernizations',
      autoHeight: true,
      wrapText: true,
    },
    echelonApplicability: {
      field: 'echelonApplicability',
      headerName: 'Echelon Applicability',
      autoHeight: true,
      wrapText: true,
    },
    transitionInContactLineOfEffort: {
      field: 'transitionInContactLineOfEffort',
      headerName: 'TiC Line of Effort',
      autoHeight: true,
      wrapText: true,
    },
    operationalRules: {
      field: 'operationalRules',
      headerName: 'Operational Rules',
      autoHeight: true,
      wrapText: true,
    },
    capabilityArea: {
      field: 'capabilityArea',
      headerName: 'Capability Area',
      autoHeight: true,
      wrapText: true,
    },
    categories: {
      field: 'categories',
      headerName: 'Categories',
      autoHeight: true,
      wrapText: true,

      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return params.data?.categories.map((category) => (category.name ? `${category.name}` : ''));
      },
    },
    solutionPathway: {
      field: 'solutionPathway',
      headerName: 'Solution Pathway',
      autoHeight: true,
      wrapText: true,
    },
    statusNotes: {
      field: 'statusNotes',
      headerName: 'Status Notes',
      autoHeight: true,
      wrapText: true,
    },
  } as Record<string, any>;
};
