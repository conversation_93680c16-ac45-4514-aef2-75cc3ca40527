import { observer } from 'mobx-react';
import React, { Component } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Debouncer, LabeledInput } from '../../../lib';
import { Column, FieldRow, InputSet } from '../../../lib/ui/molecules/InputSet';
import { UpdateOperator, Stakeholder } from '../../../services/codegen/types';
import { StakeholderStore, StakeholderSearchStore } from '../../../stores/StakeholderStore';

interface OpportunityStakeholderProps {
  style?: StyleProp<ViewStyle>;
  getStakeholderStore: () => StakeholderStore;
  getStakeholders: () => Stakeholder[] | undefined;
  onStakeholderChanged: (operator: UpdateOperator, stakeholderId: string) => Promise<void>;
  getEditable: () => boolean;
  theme: ReactNativePaper.ThemeProp;
}

@observer
class OpportunityStakeholders extends Component<OpportunityStakeholderProps> {
  private debouncer = Debouncer();
  render() {
    const { getStakeholderStore, getStakeholders, getEditable, style, theme } = this.props;
    const nameSearchStore = getStakeholderStore().getStakeholderSearchStore('opp_name');
    const orgSearchStore = getStakeholderStore().getStakeholderSearchStore('opp_org');
    return (
      <View style={[{ zIndex: 2 }, style]}>
        <InputSet
          rowLabel="Stakeholder"
          style={{}}
          getColumns={this.getColumns}
          getRows={() => this.getRows(getStakeholders)}
          getSearchValue={(column) => this.handleGetSearchValue(nameSearchStore, orgSearchStore, column) || ''}
          setSearchValue={(column, value) => this.handleSetSearchValue(nameSearchStore, orgSearchStore, column, value)}
          getSearchResults={(column) => this.getSearchResults(column, nameSearchStore, orgSearchStore)}
          addText="Add New Stakeholder"
          onSubmitNewValues={() => this.handleOnSubmitStakeholder(nameSearchStore, orgSearchStore)}
          onRemoveRow={(row) => this.handleOnRemoveStakeholder(row)}
          allowReorder={false}
          getEditable={getEditable}
        />
      </View>
    );
  }

  componentWillUnmount() {
    const { getStakeholderStore } = this.props;
    getStakeholderStore().clearAll();
  }

  getColumns = () => [
    { fieldName: 'name', label: 'Contact Name' },
    { fieldName: 'org', label: 'Organization' },
  ];

  getRows = (getStakeholders: () => Stakeholder[] | undefined) => {
    return (
      getStakeholders()?.map((stakeholder) => ({
        id: stakeholder.id,
        fieldValues: [stakeholder.name || '', stakeholder.org || ''],
      })) || []
    );
  };

  getSearchResults = (
    column: Column,
    nameSearchStore: StakeholderSearchStore,
    orgSearchStore: StakeholderSearchStore,
  ) => {
    let values;
    if (column.fieldName === 'name') values = nameSearchStore.uniqueMatches('name');
    else if (column.fieldName === 'org') values = orgSearchStore.uniqueMatches('org');
    return (
      values?.map((value) => ({
        label: value,
        value,
      })) || []
    );
  };

  handleOnRemoveStakeholder = (row: FieldRow) => {
    const stakeholderId = row.id;
    const { onStakeholderChanged } = this.props;
    onStakeholderChanged(UpdateOperator.Rm, stakeholderId);
  };

  handleOnPress = (text: string) => {
    //this.handleOnSearchValueChange(text);
  };

  handleOnSubmitStakeholder = async (
    nameSearchStore: StakeholderSearchStore,
    orgSearchStore: StakeholderSearchStore,
  ) => {
    const { getStakeholderStore, onStakeholderChanged } = this.props;
    const stakeholderStore = getStakeholderStore();
    try {
      const stakeholder = await stakeholderStore.addStakeholder({
        name: nameSearchStore.getSearchValue('name'),
        org: orgSearchStore.getSearchValue('org'),
      });
      if (onStakeholderChanged) await onStakeholderChanged(UpdateOperator.Add, stakeholder.id);
      nameSearchStore.clearAll();
      orgSearchStore.clearAll();
    } catch (error) {
      stakeholderStore.addError(error as any, false);
    }
  };

  handleGetSearchValue = (
    nameSearchStore: StakeholderSearchStore,
    orgSearchStore: StakeholderSearchStore,
    column: Column,
  ) => {
    if (column.fieldName === 'name') return nameSearchStore.getSearchValue('name');
    if (column.fieldName === 'org') return orgSearchStore.getSearchValue('org');
  };

  handleSetSearchValue = (
    nameSearchStore: StakeholderSearchStore,
    orgSearchStore: StakeholderSearchStore,
    column: Column,
    value: any,
  ) => {
    if (column.fieldName === 'name') nameSearchStore.setFieldSearchValue({ fieldName: 'name', value });
    else if (column.fieldName === 'org') orgSearchStore.setFieldSearchValue({ fieldName: 'org', value });
    this.debouncer(() => {
      if (column.fieldName === 'name') nameSearchStore.queryStakeholders();
      else if (column.fieldName === 'org') orgSearchStore.queryStakeholders();
    }, 500);
    //.clearErrorMessage();
  };
}

export default withTheme(OpportunityStakeholders);
