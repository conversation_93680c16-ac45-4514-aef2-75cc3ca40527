import { observer, useLocalObservable } from 'mobx-react';
import { Group } from '../../../../lib/ui/atoms/Group';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps } from '../CuratedOpportunity';
import { View } from 'react-native';
import { <PERSON><PERSON>, Text } from '../../../../lib';
import { OpportunityOwner, OpportunityOwnerStatus } from '../../../../services/codegen/types';
import { OpportunityOwnersStore } from '../../../../stores/OpportunityOwnersStore';
import { EditOpportunityOwner } from '../EditOpportunityOwner';
import { OpportunityOwnerCard } from '../OpportunityOwnerCard';
import { OpportunityOwnerTypeAheadInput } from '../OpportunityOwnerTypeAheadInput';
import { runInAction, toJS } from 'mobx';
import { ViewRemovedOwnersDialog } from '../ViewRemovedOwnersDialog';

interface OpportunityOwnersProps extends CuratedOpportunityGroupProps {
  opportunityOwnersStore: OpportunityOwnersStore;
}

// TODO
// Validation
// Break the thing
// Refactor

export interface OpportunityOwnersLocals {
  showSearch: boolean;
  toggleSearch: () => void;
  showInputs: boolean;
  toggleShowInputs: () => void;
  showDialog: boolean;
  setShowDialog: (value: boolean) => void;
}

export const OpportunityOwners = withTheme(
  observer(({ editable, label, opportunityStore, opportunityOwnersStore, theme }: OpportunityOwnersProps) => {
    const { fonts, colors, fontSizes } = theme;

    const localStore = useLocalObservable<OpportunityOwnersLocals>(() => ({
      showSearch: false,
      toggleSearch: () => {
        localStore.showSearch = !localStore.showSearch;
      },
      showInputs: false,
      toggleShowInputs: () => {
        localStore.showInputs = !localStore.showInputs;
      },
      showDialog: false,
      setShowDialog: (value: boolean) => {
        localStore.showDialog = value;
      },
    }));

    const owners = opportunityStore.opportunity?.owners ?? [];

    const initialOwner = opportunityStore.initialOwner;
    const currentOwner = opportunityStore.currentOwner;
    const previousOwners = owners
      .filter((owner) => {
        return owner.status === OpportunityOwnerStatus.Previous && owner.isRemoved !== true;
      })
      .sort((a, b) => {
        // Sort by removedAt descending (latest removed first)
        return new Date(b.removedAt ?? 0).getTime() - new Date(a.removedAt ?? 0).getTime();
      });
    const shouldShowAddNew = !localStore.showSearch && !localStore.showInputs;
    const removedOwners = owners.filter((owner) => owner.isRemoved === true);
    const areThereRemovedOwners = removedOwners.length > 0;
    const areTherePreviousOwners = previousOwners.length > 0;
    return (
      <>
        <Group
          title="Opportunity Owners"
          style={{ zIndex: 2 }}
          contentStyle={{ gap: 32 }}
          description={label}
          noBorder={true}
        >
          {currentOwner ? (
            <View style={{ gap: 16 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={{ color: colors.secondaryTextColor }}>Opportunity Owner</Text>
                {editable ? (
                  <Button
                    labelStyle={[
                      { color: colors.secondaryTextColor, textTransform: 'capitalize' },
                      fonts.mediumTitle,
                      fontSizes.xSmall,
                    ]}
                    style={{ height: 24 }}
                    type="secondary"
                    onPress={() => onCreateNewButtonPress(localStore, opportunityOwnersStore)}
                  >
                    {shouldShowAddNew ? 'Add New Owner' : 'Cancel'}
                  </Button>
                ) : null}
              </View>
              <View>
                {localStore.showSearch && editable ? (
                  <OpportunityOwnerTypeAheadInput opportunityOwnersStore={opportunityOwnersStore} locals={localStore} />
                ) : null}
                {localStore.showInputs ? (
                  <EditOpportunityOwner
                    opportunityOwnersStore={opportunityOwnersStore}
                    opportunityStore={opportunityStore}
                    locals={localStore}
                  />
                ) : null}
              </View>
              <OpportunityOwnerCard
                opportunityOwnerStore={opportunityOwnersStore}
                opportunityStore={opportunityStore}
                theme={theme}
                opportunityOwner={currentOwner}
              />
            </View>
          ) : null}
          {areTherePreviousOwners || areThereRemovedOwners ? (
            <View style={{ gap: 4, zIndex: -1 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={{ color: colors.secondaryTextColor }}>Previous Opportunity Owners</Text>
                {editable && areThereRemovedOwners ? (
                  <Button
                    labelStyle={[
                      { color: colors.secondaryTextColor, textTransform: 'capitalize' },
                      fonts.mediumTitle,
                      fontSizes.xSmall,
                    ]}
                    style={{ height: 24 }}
                    type="secondary"
                    onPress={() => {
                      localStore.setShowDialog(true);
                    }}
                  >
                    View Removed
                  </Button>
                ) : null}
              </View>
              <View style={{ gap: 16 }}>
                {previousOwners.map((owner) => (
                  <OpportunityOwnerCard
                    opportunityOwnerStore={opportunityOwnersStore}
                    opportunityStore={opportunityStore}
                    key={owner.id}
                    theme={theme}
                    opportunityOwner={owner}
                  />
                ))}
              </View>
            </View>
          ) : null}
          <View style={{ gap: currentOwner ? 4 : 16, zIndex: -1 }}>
            {!currentOwner ? (
              <View style={{ gap: 16 }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ color: colors.secondaryTextColor }}>Original Submitter</Text>
                  {editable ? (
                    <Button
                      labelStyle={[
                        { color: colors.secondaryTextColor, textTransform: 'capitalize' },
                        fonts.mediumTitle,
                        fontSizes.xSmall,
                      ]}
                      style={{ height: 24 }}
                      type="secondary"
                      onPress={() => onCreateNewButtonPress(localStore, opportunityOwnersStore)}
                    >
                      {shouldShowAddNew ? 'Add New Owner' : 'Cancel'}
                    </Button>
                  ) : null}
                </View>
                <View>
                  {localStore.showSearch && editable ? (
                    <OpportunityOwnerTypeAheadInput
                      opportunityOwnersStore={opportunityOwnersStore}
                      locals={localStore}
                    />
                  ) : null}
                  {localStore.showInputs ? (
                    <EditOpportunityOwner
                      opportunityOwnersStore={opportunityOwnersStore}
                      opportunityStore={opportunityStore}
                      locals={localStore}
                    />
                  ) : null}
                </View>
              </View>
            ) : (
              <Text style={{ color: colors.secondaryTextColor }}>Original Submitter</Text>
            )}
            {initialOwner ? (
              <OpportunityOwnerCard
                opportunityOwnerStore={opportunityOwnersStore}
                opportunityStore={opportunityStore}
                theme={theme}
                opportunityOwner={initialOwner}
              />
            ) : null}
          </View>
        </Group>
        <ViewRemovedOwnersDialog
          opportunityOwnerStore={opportunityOwnersStore}
          opportunityStore={opportunityStore}
          onConfirm={() => {
            localStore.setShowDialog(false);
          }}
          getVisible={() => localStore.showDialog}
          onDismiss={() => localStore.setShowDialog(false)}
        />
      </>
    );
  }),
);

const onCreateNewButtonPress = (
  localStore: OpportunityOwnersLocals,
  opportunityOwnersStore: OpportunityOwnersStore,
) => {
  localStore.toggleSearch();
  runInAction(() => {
    opportunityOwnersStore.keywordSearchValue = '';
  });
  if (localStore.showInputs) {
    localStore.toggleShowInputs();
  }
};
