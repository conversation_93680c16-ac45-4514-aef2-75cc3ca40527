import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { OpportunityStore } from '../../../../stores';
import { Pressable, View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import {
  DependentDropdown,
  Label,
  LabeledDropdownMenu,
  LabeledInput,
  MenuGroup,
  MenuItem,
  Text,
} from '../../../../lib';
import { RadioButton } from '../../../../lib/ui/atoms/RadioButton';
import { TransitionInContactDropdown } from '../TransitionInContactDropdown';
import { OperationalRulesDropdown } from '../OperationalRulesDropdown';
import { CapabilityAreaDropdown } from '../CapabilityAreaDropdown';

interface TransitionInContactProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const TransitionInContact = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: TransitionInContactProps) => {
    const {
      styles: { components, fonts, fontSizes, paddings },
    } = theme;

    return (
      <>
        <View style={[components.rowStyle]}>
          <Target name={'IsTransitionInContact'}>
            <Label>Is this a Transformation in Contact (TiC) Line of Effort?</Label>
            <LabeledInput>
              {editable ? (
                <View style={{ flexDirection: 'row' }}>
                  <RadioButton
                    label="No"
                    getChecked={() => !opportunityStore.opportunity?.isTiCLOE || false}
                    getValue={() => 'No'}
                    onChecked={() => {
                      opportunityStore.setValue('isTiCLOE', false);
                      opportunityStore.setValue('transitionInContactLineOfEffort', undefined);
                      opportunityStore.setValue('operationalRules', undefined);
                      opportunityStore.setValue('capabilityArea', undefined);
                      handleDebounce(opportunityStore);
                    }}
                  />
                  <RadioButton
                    label="Yes"
                    getChecked={() => opportunityStore.opportunity?.isTiCLOE || false}
                    getValue={() => 'Yes'}
                    onChecked={() => {
                      opportunityStore.setValue('isTiCLOE', true);
                      handleDebounce(opportunityStore);
                    }}
                  />
                </View>
              ) : (
                <Text style={[fonts.regular, fontSizes.small, paddings.XS]}>
                  {opportunityStore.opportunity?.isTiCLOE ? 'Yes' : 'No'}
                </Text>
              )}
            </LabeledInput>
          </Target>
        </View>
        {opportunityStore.opportunity?.isTiCLOE && (
          <>
            <TransitionInContactDropdown
              editable={editable}
              handleDebounce={handleDebounce}
              opportunityStore={opportunityStore}
            />
            <OperationalRulesDropdown
              editable={editable}
              handleDebounce={handleDebounce}
              opportunityStore={opportunityStore}
            />
            <CapabilityAreaDropdown
              editable={editable}
              handleDebounce={handleDebounce}
              opportunityStore={opportunityStore}
            />
          </>
        )}
      </>
    );
  }),
);
