import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps } from '../CuratedOpportunity';
import { Group } from '../../../../lib/ui/atoms/Group';
import { Label, LabeledDropdownMenu, MenuItem, Text, TextInput } from '../../../../lib';
import { OpportunityStore } from '../../../../stores';
import { Pressable, View } from 'react-native';
import { Icon } from '../../../../lib/ui/atoms/Icon';
import { MultiselectMenu } from '../../../../lib/ui/atoms/MultiselectMenu';

const MaterielSolutionItems = [
  { label: 'Hardware', value: 'Hardware' },
  { label: 'Software', value: 'Software' },
  { label: 'Combination', value: 'Combination' },
  { label: 'Non-Materiel', value: 'Non-Materiel' },
];

const DOTMLPFPPItems = [
  { label: 'Doctrine', value: 'Doctrine' },
  { label: 'Organization', value: 'Organization' },
  { label: 'Training', value: 'Training' },
  { label: 'Materiel', value: 'Materiel' },
  { label: 'Leadership & Education', value: 'Leadership & Education' },
  { label: 'Personnel', value: 'Personnel' },
  { label: 'Facilities', value: 'Facilities' },
  { label: 'Policy', value: 'Policy' },
];

interface AnalysisProps extends CuratedOpportunityGroupProps {
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const Analysis = withTheme(
  observer(({ editable, opportunityStore, label, handleDebounce }: AnalysisProps) => {
    const localStore = useLocalObservable(() => ({
      isDialogOpen: false,
      selectedDOTMLPFPP: [] as string[],
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
      setSelectedDOTMLPFPP(values: string[]) {
        this.selectedDOTMLPFPP = values;
      },
    }));
    return (
      <Group title="Analysis" style={[{ zIndex: 3 }]} contentStyle={{ gap: 16 }} description={label}>
        <View>
          <Label>Feasibility Summary</Label>
          <Text>
            Explain why this problem is worth solving and whether a solution is feasible. How practical is it to solve
            this problem now, given current constraints? Is this the appropriate venue to solve this problem?{' '}
          </Text>
          <TextInput
            getValue={() => opportunityStore.getValue('feasibilitySummary')}
            setValue={(value) => opportunityStore.setValue('feasibilitySummary', value)}
            editable={editable}
            multiline
            numberOfLines={5}
            placeholder="Feasibility Summary"
            onDebounceValue={() => handleDebounce(opportunityStore)}
          />
        </View>
        <LabeledDropdownMenu
          dropdownMenuProps={{
            getEditable: () => editable,
            getMenuItems: () => MaterielSolutionItems,
            onItemSelected: (item: MenuItem) => {
              opportunityStore.setValue('materielSolutionType', item.value);
              handleDebounce(opportunityStore);
            },
            getValue: () =>
              opportunityStore.opportunity?.materielSolutionType
                ? {
                    label: opportunityStore.opportunity.materielSolutionType,
                    value: opportunityStore.opportunity.materielSolutionType,
                  }
                : {
                    label: 'Unassigned',
                    value: 'unassigned',
                  },
          }}
          labelText="Materiel Solution Type"
          icon={
            <Pressable onPress={() => localStore.setIsDialogOpen(true)}>
              <Icon name="help-circle" color="#676D79" size={24} iconStyle={{ pointerEvents: 'box-only' }} />
            </Pressable>
          }
          labelProps={{ textStyle: { gap: 8, display: 'flex', alignItems: 'center' } }}
        />
        <View>
          <Label>DOTMLPFPP</Label>
          <MultiselectMenu
            getMenuItems={() => DOTMLPFPPItems}
            getSelectedValues={() => localStore.selectedDOTMLPFPP}
            onSelectionChange={(values) => {
              localStore.setSelectedDOTMLPFPP(values);
              // You can also save to opportunityStore if needed:
              // opportunityStore.setValue('dotmlpfpp', values);
              // handleDebounce(opportunityStore);
            }}
            getEditable={() => editable}
            placeholder="Unassigned"
            showSelectAll={false}
          />
        </View>
      </Group>
    );
  }),
);
