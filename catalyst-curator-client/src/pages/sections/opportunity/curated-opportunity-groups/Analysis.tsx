import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { Group } from '../../../../lib/ui/atoms/Group';
import { DropdownMenu, Label, LabeledDropdownMenu, MenuItem, Text, TextInput } from '../../../../lib';
import { OpportunityStore } from '../../../../stores';
import { Pressable, View } from 'react-native';
import { Icon } from '../../../../lib/ui/atoms/Icon';
import { DOTMLPFPPSelector } from '../DOTMLPFPPSelector';

const MaterielSolutionItems = [
  { label: 'Hardware', value: 'Hardware' },
  { label: 'Software', value: 'Software' },
  { label: 'Combination', value: 'Combination' },
  { label: 'Non-Materiel', value: 'Non-Materiel' },
];

interface AnalysisProps extends CuratedOpportunityGroupProps {
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const Analysis = withTheme(
  observer(({ editable, theme, opportunityStore, label, handleDebounce }: AnalysisProps) => {
    const {
      styles: { components, margins, fontSizes, fonts },
      colors,
    } = theme;

    const localStore = useLocalObservable(() => ({
      isDialogOpen: false,
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
    }));
    return (
      <Group title="Analysis" style={[{ zIndex: 2 }]} contentStyle={{ gap: 16 }} description={label}>
        <View>
          <Label>Feasibility Summary</Label>
          <Text>
            Explain why this problem is worth solving and whether a solution is feasible. How practical is it to solve
            this problem now, given current constraints? Is this the appropriate venue to solve this problem?{' '}
          </Text>
          <TextInput
            getValue={() => opportunityStore.getValue('feasibilitySummary')}
            setValue={(value) => opportunityStore.setValue('feasibilitySummary', value)}
            editable={editable}
            multiline
            numberOfLines={5}
            placeholder="Feasibility Summary"
            onDebounceValue={() => handleDebounce(opportunityStore)}
          />
        </View>
        <LabeledDropdownMenu
          dropdownMenuProps={{
            getEditable: () => editable,
            getMenuItems: () => MaterielSolutionItems,
            onItemSelected: (item: MenuItem) => {
              opportunityStore.setValue('materielSolutionType', item.value);
              handleDebounce(opportunityStore);
            },
            getValue: () =>
              opportunityStore.opportunity?.materielSolutionType
                ? {
                    label: opportunityStore.opportunity.materielSolutionType,
                    value: opportunityStore.opportunity.materielSolutionType,
                  }
                : {
                    label: 'Unassigned',
                    value: 'unassigned',
                  },
          }}
          labelText="Materiel Solution Type"
          icon={
            <Pressable onPress={() => localStore.setIsDialogOpen(true)}>
              <Icon name="help-circle" color="#676D79" size={24} iconStyle={{ pointerEvents: 'box-only' }} />
            </Pressable>
          }
          labelProps={{ textStyle: { gap: 8, display: 'flex', alignItems: 'center' } }}
        />
        <DropdownMenu />
        <DOTMLPFPPSelector />
      </Group>
    );
  }),
);
