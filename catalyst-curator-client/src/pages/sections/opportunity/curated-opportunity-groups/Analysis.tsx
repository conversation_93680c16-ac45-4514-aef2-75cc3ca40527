import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps } from '../CuratedOpportunity';
import { Group } from '../../../../lib/ui/atoms/Group';
import { Button, Label, LabeledDropdownMenu, MenuItem, Text, TextInput } from '../../../../lib';
import { OpportunityStore } from '../../../../stores';
import { Linking, Pressable, View } from 'react-native';
import { Icon } from '../../../../lib/ui/atoms/Icon';
import { MultiselectMenu } from '../../../../lib/ui/atoms/MultiselectMenu';
import { AddExistingSolution } from '../AddExistingSolution';
import { IconButton } from '../../../../lib/ui/atoms/IconButton';

const MaterielSolutionItems = [
  { label: 'Hardware', value: 'Hardware' },
  { label: 'Software', value: 'Software' },
  { label: 'Combination', value: 'Combination' },
  { label: 'Non-Materiel', value: 'Non-Materiel' },
];

const DOTMLPFPPItems = [
  { label: 'No Change', value: 'No Change', exclusive: true, separatorAfter: true },
  { label: 'Doctrine', value: 'Doctrine' },
  { label: 'Organization', value: 'Organization' },
  { label: 'Training', value: 'Training' },
  { label: 'Materiel', value: 'Materiel' },
  { label: 'Leadership & Education', value: 'Leadership & Education' },
  { label: 'Personnel', value: 'Personnel' },
  { label: 'Facilities', value: 'Facilities' },
  { label: 'Policy', value: 'Policy' },
];

interface AnalysisProps extends CuratedOpportunityGroupProps {
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const Analysis = withTheme(
  observer(({ editable, opportunityStore, label, handleDebounce, theme }: AnalysisProps) => {
    const localStore = useLocalObservable(() => ({
      isDialogOpen: false,
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
      isNewExistingSolutionOpen: false,
      setIsNewExistingSolutionOpen(value: boolean) {
        this.isNewExistingSolutionOpen = value;
      },
    }));

    const {
      colors,
      fonts,
      styles: { components },
    } = theme;

    return (
      <Group title="Analysis" style={[{ zIndex: 3 }]} contentStyle={{ gap: 16 }} description={label}>
        <View>
          <Label>Feasibility Summary</Label>
          <Text>
            Explain why this problem is worth solving and whether a solution is feasible. How practical is it to solve
            this problem now, given current constraints? Is this the appropriate venue to solve this problem?{' '}
          </Text>
          <TextInput
            getValue={() => opportunityStore.getValue('feasibilitySummary')}
            setValue={(value) => opportunityStore.setValue('feasibilitySummary', value)}
            editable={editable}
            multiline
            numberOfLines={5}
            placeholder="Feasibility Summary"
            onDebounceValue={() => handleDebounce(opportunityStore)}
          />
        </View>
        <LabeledDropdownMenu
          dropdownMenuProps={{
            getEditable: () => editable,
            getMenuItems: () => MaterielSolutionItems,
            onItemSelected: (item: MenuItem) => {
              opportunityStore.setValue('materielSolutionType', item.value);
              handleDebounce(opportunityStore);
            },
            getValue: () =>
              opportunityStore.opportunity?.materielSolutionType
                ? {
                    label: opportunityStore.opportunity.materielSolutionType,
                    value: opportunityStore.opportunity.materielSolutionType,
                  }
                : {
                    label: 'Unassigned',
                    value: 'unassigned',
                  },
          }}
          labelText="Materiel Solution Type"
          icon={
            <Pressable onPress={() => localStore.setIsDialogOpen(true)}>
              <Icon name="help-circle" color="#676D79" size={24} iconStyle={{ pointerEvents: 'box-only' }} />
            </Pressable>
          }
          labelProps={{ textStyle: { gap: 8, display: 'flex', alignItems: 'center' } }}
        />
        <View>
          <Label>DOTMLPF-P Change</Label>
          <MultiselectMenu
            getMenuItems={() => DOTMLPFPPItems}
            getSelectedValues={() => opportunityStore.opportunity?.DOTMLPFPPChange || []}
            onSelectionChange={(values) => {
              opportunityStore.setValue('DOTMLPFPPChange', values);
              handleDebounce(opportunityStore);
            }}
            getEditable={() => editable}
            placeholder="Unassigned"
            showSelectAll={false}
          />
        </View>
        <View style={{ gap: 16 }}>
          <Label>Existing Solutions for Rapid Fielding</Label>
          <Text>
            Search for solutions in the
            <Text />{' '}
            <Text
              style={{ color: 'blue', textDecorationLine: 'underline' }}
              onPress={() => Linking.openURL('https://armyeitaas.sharepoint-mil.us/teams/101stEagleEye')}
            >
              Warrior Design Depot
            </Text>{' '}
            <Text>or in the </Text>
            <Text
              style={{ color: 'blue', textDecorationLine: 'underline' }}
              onPress={() =>
                Linking.openURL('https://www.dla.mil/Information-Operations/Services/Applications/WebFLIS/')
              }
            >
              Defense Logistics Agency NSN list.
            </Text>
          </Text>
          {localStore.isNewExistingSolutionOpen ? (
            <AddExistingSolution
              setIsOpen={(value) => localStore.setIsNewExistingSolutionOpen(value)}
              opportunityStore={opportunityStore}
            />
          ) : (
            <View style={{ flexDirection: 'row' }}>
              <Button
                labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                type="secondary"
                compact
                onPress={() => localStore.setIsNewExistingSolutionOpen(true)}
                iconName="plus-box-outline"
                iconRight
              >
                Add Existing Solution
              </Button>
            </View>
          )}
          {opportunityStore.opportunity?.existingSolutions.map((solution) => {
            return (
              <View style={{ backgroundColor: 'white', padding: 12, gap:2 borderWidth: 1, borderColor: colors.border }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <View style={{ flexDirection: 'row', gap: 8 }}>
                    <Text>{solution.needsModification ? 'Needs Modification' : 'Ready'}</Text>
                    <Text>{solution.title}</Text>
                  </View>
                  <IconButton icon="pencil" color={colors.secondaryTextColor} />
                </View>
                {solution.organization ? <Text>Organization of Origin: {solution.organization}</Text> : null}
                <Text
                  style={{ color: 'blue', textDecorationLine: 'underline' }}
                  onPress={() => Linking.openURL(solution.source)}
                >
                  {solution.source}
                </Text>
                {solution.needsModification ? (
                  <Text style={{ color: colors.secondaryTextColor }}>
                    Solution needs modification in order to be fielded.
                  </Text>
                ) : (
                  <Text style={{ color: colors.secondaryTextColor }}>Solution is ready for fielding</Text>
                )}
              </View>
            );
          })}
        </View>
      </Group>
    );
  }),
);
