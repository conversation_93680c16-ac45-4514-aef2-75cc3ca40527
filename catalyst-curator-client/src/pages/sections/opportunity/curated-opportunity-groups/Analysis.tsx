import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { Group } from '../../../../lib/ui/atoms/Group';
import { Label, Text, TextInput } from '../../../../lib';

interface AnalysisProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
}

export const Analysis = withTheme(
  observer(({ editable, theme, opportunityStore, label, getLabeledTextInput }: AnalysisProps) => {
    const {
      styles: { components, margins, fontSizes, fonts },
      colors,
    } = theme;

    return (
      <Group title="Attachments" style={[{ zIndex: 2 }]} description={label}>
        <Label>Feasibility Summary</Label>
        <Text>
          Explain why this problem is worth solving and whether a solution is feasible. How practical is it to solve
          this problem now, given current constraints? Is this the appropriate venue to solve this problem?{' '}
        </Text>
        <TextInput />
      </Group>
    );
  }),
);
