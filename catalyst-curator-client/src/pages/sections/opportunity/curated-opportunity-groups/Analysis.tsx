import { observer, useLocalObservable } from 'mobx-react';
import { Linking, Pressable, View } from 'react-native';
import { withTheme } from 'react-native-paper';

import { Button, Label, LabeledDropdownMenu, MenuItem, Text, TextInput } from '../../../../lib';
import { Group } from '../../../../lib/ui/atoms/Group';
import { Icon } from '../../../../lib/ui/atoms/Icon';
import { IconButton } from '../../../../lib/ui/atoms/IconButton';
import { MultiselectMenu } from '../../../../lib/ui/atoms/MultiselectMenu';
import { ExistingSolution } from '../../../../services/codegen/types';
import { OpportunityStore } from '../../../../stores';
import { AddExistingSolution } from '../AddExistingSolution';
import { CuratedOpportunityGroupProps } from '../CuratedOpportunity';

const MATERIEL_SOLUTION_ITEMS = [
  { label: 'Hardware', value: 'Hardware' },
  { label: 'Software', value: 'Software' },
  { label: 'Combination', value: 'Combination' },
  { label: 'Non-Materiel', value: 'Non-Materiel' },
];

const DOTMLPFPP_ITEMS = [
  { label: 'No Change', value: 'No Change', exclusive: true, separatorAfter: true },
  { label: 'Doctrine', value: 'Doctrine' },
  { label: 'Organization', value: 'Organization' },
  { label: 'Training', value: 'Training' },
  { label: 'Materiel', value: 'Materiel' },
  { label: 'Leadership & Education', value: 'Leadership & Education' },
  { label: 'Personnel', value: 'Personnel' },
  { label: 'Facilities', value: 'Facilities' },
  { label: 'Policy', value: 'Policy' },
];

const EXTERNAL_LINKS = {
  WARRIOR_DESIGN_DEPOT: 'https://armyeitaas.sharepoint-mil.us/teams/101stEagleEye',
  DLA_NSN_LIST: 'https://www.dla.mil/Information-Operations/Services/Applications/WebFLIS/',
} as const;

interface AnalysisProps extends CuratedOpportunityGroupProps {
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const Analysis = withTheme(
  observer(({ editable, opportunityStore, label, handleDebounce, theme }: AnalysisProps) => {
    const localStore = useLocalObservable(() => ({
      isDialogOpen: false,
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
      isNewExistingSolutionOpen: false,
      setIsNewExistingSolutionOpen(value: boolean) {
        this.isNewExistingSolutionOpen = value;
      },
      editingSolutionId: null as string | null,
      setEditingSolutionId(id: string | null) {
        this.editingSolutionId = id;
      },
    }));

    const { colors, fonts } = theme;

    const renderSolution = (solution: ExistingSolution) => {
      if (localStore.editingSolutionId === solution.id) {
        return (
          <AddExistingSolution
            key={solution.id}
            opportunityStore={opportunityStore}
            defaultExistingSolution={solution}
            setIsOpen={() => localStore.setEditingSolutionId(null)}
          />
        );
      }

      return (
        <View
          key={solution.id}
          style={{
            backgroundColor: 'white',
            padding: 12,
            gap: 12,
            borderRadius: 4,
            borderWidth: 1,
            borderColor: colors.border,
          }}
        >
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <View style={{ flexDirection: 'row', gap: 8 }}>
              <Text>{solution.needsModification ? 'Needs Modification' : 'Ready'}</Text>
              <Text style={fonts.medium}>{solution.title}</Text>
            </View>
            <IconButton
              icon="pencil"
              color={colors.secondaryTextColor}
              onPress={() => localStore.setEditingSolutionId(solution.id)}
            />
          </View>
          {solution.organization && <Text>Organization of Origin: {solution.organization}</Text>}
          <Text
            style={{ color: 'blue', textDecorationLine: 'underline' }}
            onPress={() => Linking.openURL(solution.source)}
          >
            {solution.source}
          </Text>
          <Text style={{ color: colors.secondaryTextColor }}>
            {solution.needsModification
              ? 'Solution needs modification in order to be fielded.'
              : 'Solution is ready for fielding'}
          </Text>
        </View>
      );
    };

    return (
      <Group title="Analysis" style={[{ zIndex: 3 }]} contentStyle={{ gap: 16 }} description={label}>
        <View>
          <Label>Feasibility Summary</Label>
          <Text>
            Explain why this problem is worth solving and whether a solution is feasible. How practical is it to solve
            this problem now, given current constraints? Is this the appropriate venue to solve this problem?{' '}
          </Text>
          <TextInput
            getValue={() => opportunityStore.getValue('feasibilitySummary')}
            setValue={(value) => opportunityStore.setValue('feasibilitySummary', value)}
            editable={editable}
            multiline
            numberOfLines={5}
            placeholder="Feasibility Summary"
            onDebounceValue={() => handleDebounce(opportunityStore)}
          />
        </View>
        <LabeledDropdownMenu
          dropdownMenuProps={{
            getEditable: () => editable,
            getMenuItems: () => MATERIEL_SOLUTION_ITEMS,
            onItemSelected: (item: MenuItem) => {
              opportunityStore.setValue('materielSolutionType', item.value);
              handleDebounce(opportunityStore);
            },
            getValue: () =>
              opportunityStore.opportunity?.materielSolutionType
                ? {
                    label: opportunityStore.opportunity.materielSolutionType,
                    value: opportunityStore.opportunity.materielSolutionType,
                  }
                : {
                    label: 'Unassigned',
                    value: 'unassigned',
                  },
          }}
          labelText="Materiel Solution Type"
          icon={
            <Pressable onPress={() => localStore.setIsDialogOpen(true)}>
              <Icon name="help-circle" color="#676D79" size={24} iconStyle={{ pointerEvents: 'box-only' }} />
            </Pressable>
          }
          labelProps={{ textStyle: { gap: 8, display: 'flex', alignItems: 'center' } }}
        />
        <View>
          <Label>DOTMLPF-P Change</Label>
          <MultiselectMenu
            getMenuItems={() => DOTMLPFPP_ITEMS}
            getSelectedValues={() => opportunityStore.opportunity?.DOTMLPFPPChange || []}
            onSelectionChange={(values) => {
              opportunityStore.setValue('DOTMLPFPPChange', values);
              handleDebounce(opportunityStore);
            }}
            getEditable={() => editable}
            placeholder="Unassigned"
            showSelectAll={false}
          />
        </View>
        <View style={{ gap: 16 }}>
          <Label>Existing Solutions for Rapid Fielding</Label>
          <Text>
            Search for solutions in the{' '}
            <Text
              style={{ color: 'blue', textDecorationLine: 'underline' }}
              onPress={() => Linking.openURL(EXTERNAL_LINKS.WARRIOR_DESIGN_DEPOT)}
            >
              Warrior Design Depot
            </Text>{' '}
            or in the{' '}
            <Text
              style={{ color: 'blue', textDecorationLine: 'underline' }}
              onPress={() => Linking.openURL(EXTERNAL_LINKS.DLA_NSN_LIST)}
            >
              Defense Logistics Agency NSN list
            </Text>
            .
          </Text>
          {localStore.isNewExistingSolutionOpen ? (
            <AddExistingSolution
              setIsOpen={(value) => localStore.setIsNewExistingSolutionOpen(value)}
              opportunityStore={opportunityStore}
            />
          ) : (
            <View style={{ flexDirection: 'row' }}>
                
              <Button
                labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                type="secondary"
                compact
                onPress={() => localStore.setIsNewExistingSolutionOpen(true)}
                iconName="plus-box-outline"
                iconRight
              >
                Add Existing Solution
              </Button>
            </View>
          )}
          {opportunityStore.opportunity?.existingSolutions.map(renderSolution)}
        </View>
      </Group>
    );
  }),
);
