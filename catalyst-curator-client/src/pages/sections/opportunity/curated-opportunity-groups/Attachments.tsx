import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { CollapsibleView } from '../../../../lib/ui/atoms/CollapsibleView';
import { Text } from '../../../../lib/ui/atoms/Text';
import { OpportunityStore } from '../../../../stores';
import { AttachmentInfo } from '../../../../lib/ui/molecules/FileAttachment';
import { FileAttachments } from '../../../../lib/ui/molecules/FileAttachments';
import { Attachment, Location } from '../../../../services/codegen/types';
import { openWebFileLink } from '../../../../utilities/File';

interface WarfightingFunctionProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
}

export const Attachments = withTheme(
  observer(({ editable, theme, opportunityStore, label, getLabeledTextInput }: WarfightingFunctionProps) => {
    const {
      styles: { components, margins, fontSizes, fonts },
      colors,
    } = theme;

    return (
      <Group title="Attachments" style={[{ zIndex: 2 }]} description={label}>
        <FileAttachments
          style={margins.BottomL}
          getAttachments={() => opportunityStore.opportunity?.attachments as AttachmentInfo[]}
          onPress={(id) => handleOnRetrieveDocument(opportunityStore, id)}
          onDelete={(id) => handleOnDeleteDocument(opportunityStore, id)}
          getIsInProgress={() => opportunityStore.fileUploadInProgress}
          onAddAttachment={async (result) => await handleOnAddDocument(result, opportunityStore)}
          getEditable={() => editable}
        />
        {
          // show in readonly mode if it's not empty
          !editable &&
            opportunityStore.getValue('attachmentNotes') &&
            getLabeledTextInput({
              opportunityStore,
              fieldName: 'attachmentNotes',
              labelText: 'Notes About Attached Files',
              textInputProps: {
                multiline: true,
                placeholder: 'Additional information about attached files',
              },
              editable,
              style: [components.rowStyle],
            })
        }
        {
          // show in edit mode, expanded if data present
          editable && (
            <CollapsibleView
              header={
                <Text style={[fontSizes.mediumSmall, fonts.medium, { color: colors.primary }]}>ATTACHMENT NOTES</Text>
              }
              contentStyle={[margins.VerticalM]}
              defaultOpen={!!opportunityStore.getValue('attachmentNotes')}
            >
              {getLabeledTextInput({
                opportunityStore,
                fieldName: 'attachmentNotes',
                labelText: 'Notes About Attached Files',
                textInputProps: {
                  multiline: true,
                  placeholder: 'Additional information about attached files',
                },
                editable,
                style: [components.rowStyle],
              })}
            </CollapsibleView>
          )
        }
      </Group>
    );
  }),
);

const handleOnRetrieveDocument = (opportunityStore: OpportunityStore, attachmentId: string): void => {
  const attachment = opportunityStore.opportunity?.attachments.find((a: Attachment) => a.id === attachmentId);
  opportunityStore.getAttachment(attachmentId).then((loc: Location) => {
    return openWebFileLink(loc.location, attachment?.name || '', attachment?.mimetype || '');
  });
};

const handleOnAddDocument = async (
  result: { file: File; uri: string },
  opportunityStore: OpportunityStore,
): Promise<void> => {
  await opportunityStore.addAttachment(result);
};

const handleOnDeleteDocument = (opportunityStore: OpportunityStore, attachmentId: string): void => {
  opportunityStore.deleteAttachment(attachmentId);
};
