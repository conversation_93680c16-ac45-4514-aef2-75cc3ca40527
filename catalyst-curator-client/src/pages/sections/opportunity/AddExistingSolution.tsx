import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, Label, TextInput } from '../../../lib';
import { Checkbox } from '../../../lib/ui/atoms/Checkbox';
import { OpportunityStore } from '../../../stores';
import { ExistingSolution } from '../../../services/codegen/types';
import { toJS } from 'mobx';

type AddExistingSolutionProps = {
  opportunityStore: OpportunityStore;
  theme: ReactNativePaper.ThemeProp;
  defaultExistingSolution?: ExistingSolution;
  setIsOpen: (value: boolean) => void;
};
export const AddExistingSolution = withTheme(
  observer(({ opportunityStore, theme, defaultExistingSolution, setIsOpen }: AddExistingSolutionProps) => {
    const {
      colors,
      fonts,
      styles: { components },
    } = theme;

    const localStore = useLocalObservable(() => ({
      source: defaultExistingSolution?.source || '',
      setSource(value: string) {
        this.source = value;
      },

      title: defaultExistingSolution?.title || '',
      setTitle(value: string) {
        this.title = value;
      },
      organization: defaultExistingSolution?.organization || '',
      setOrganization(value: string) {
        this.organization = value;
      },
      needsModification: defaultExistingSolution?.needsModification || false,
      setNeedsModification(value: boolean) {
        this.needsModification = value;
      },
    }));

    function isValid() {
      return localStore.source !== '';
    }

    console.log({ defaultExistingSolution: toJS(defaultExistingSolution) });
    async function handleSave() {
      if (!isValid()) return;
      if (defaultExistingSolution) {
        await opportunityStore.updateExistingSolution(defaultExistingSolution.id, {
          source: localStore.source,
          title: localStore.title,
          organization: localStore.organization,
          needsModification: localStore.needsModification,
        });
      } else {
        await opportunityStore.addExistingSolution({
          source: localStore.source,
          title: localStore.title,
          organization: localStore.organization,
          needsModification: localStore.needsModification,
        });
      }
      setIsOpen(false);
    }

    async function handleDelete() {
      if (defaultExistingSolution) {
        await opportunityStore.deleteExistingSolution(defaultExistingSolution.id);
      }
      localStore.setSource('');
      localStore.setTitle('');
      localStore.setOrganization('');
      localStore.setNeedsModification(false);
      setIsOpen(false);
    }

    return (
      <View
        style={{
          gap: 12,
          backgroundColor: 'white',
          padding: 12,
          borderWidth: 1,
          borderColor: colors.border,
          borderRadius: 4,
        }}
      >
        <View>
          <Label>Source (URL)</Label>
          <TextInput getValue={() => localStore.source} setValue={(source) => localStore.setSource(source)} />
        </View>
        <View>
          <Label>Title (Optional)</Label>
          <TextInput getValue={() => localStore.title} setValue={(title) => localStore.setTitle(title)} />
        </View>
        <View>
          <Label>Organization of Origin (Optional)</Label>
          <TextInput
            getValue={() => localStore.organization}
            setValue={(organization) => localStore.setOrganization(organization)}
          />
        </View>
        <Checkbox
          getChecked={() => localStore.needsModification}
          label={'Solution needs modification in order to be fielded'}
          onChecked={() => {
            localStore.setNeedsModification(true);
          }}
          onUnchecked={() => {
            localStore.setNeedsModification(false);
          }}
        />
        <View style={{ flexDirection: 'row', alignSelf: 'flex-end', gap: 12 }}>
          <Button
            labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
            type="secondary"
            onPress={handleDelete}
          >
            Delete
          </Button>
          <Button
            labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
            type="primary"
            onPress={handleSave}
            getDisabled={() => !isValid()}
          >
            Save
          </Button>
        </View>
      </View>
    );
  }),
);
