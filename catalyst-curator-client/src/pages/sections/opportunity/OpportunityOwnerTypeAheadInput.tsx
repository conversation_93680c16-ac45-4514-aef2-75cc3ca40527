import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps } from './CuratedOpportunity';
import { Pressable } from 'react-native';
import { DebounceFn, Debouncer, Text } from '../../../lib';
import Icons from '@expo/vector-icons/MaterialCommunityIcons';
import { OpportunityOwnersStore } from '../../../stores/OpportunityOwnersStore';
import { TypeAheadInput } from '../../../lib/ui/molecules/TypeAheadInput';
import { useRef } from 'react';
import { OpportunityStore } from '../../../stores';
import { OpportunityOwnersLocals } from './curated-opportunity-groups/OpportunityOwners';
import { runInAction } from 'mobx';

interface OpportunityOwnerTypeAheadInputProps {
  opportunityOwnersStore: OpportunityOwnersStore;
  locals: OpportunityOwnersLocals;
  theme: ReactNativePaper.ThemeProp;
}

export const OpportunityOwnerTypeAheadInput = withTheme(
  observer(({ opportunityOwnersStore, locals, theme }: OpportunityOwnerTypeAheadInputProps) => {
    const {
      styles: { components, margins },
      colors,
    } = theme;

    const debouncerRef = useRef(Debouncer()).current;

    return (
      <TypeAheadInput
        dropDownStyle={[
          {
            padding: 8,
            backgroundColor: colors.background,
            borderRadius: 0,
            borderWidth: 0,
          },
          components.shadow,
        ]}
        getValue={() => opportunityOwnersStore.keywordSearchValue}
        onValueChange={(value) => {
          handleOnSearchValueChange(debouncerRef, opportunityOwnersStore, value);
        }}
        onItemSelected={(item) => {}}
        onSubmit={() => {}}
        placeholder="Search for an owner by name or email"
        getDropdownItems={() => {
          return opportunityOwnersStore.opportunityOwnersSearchResult.map((owner) => {
            return {
              label: `${owner.lastName}, ${owner.firstName}`,
              subLabel: owner.emailAddress,
              value: owner,
            };
          });
        }}
        renderItem={({ item, index }) => {
          return (
            <Pressable
              key={index}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                backgroundColor: colors.background,
              }}
              onPress={() => {
                opportunityOwnersStore.setSelectedSearchOwnerResult(item.value);
                runInAction(() => {
                  opportunityOwnersStore.keywordSearchValue = '';
                });
                locals.toggleShowInputs();
                locals.toggleSearch();
              }}
            >
              <Text style={{ fontSize: 14 }}>{item.label}</Text>
              <Text style={{ color: colors.secondaryTextColor, fontSize: 14 }}>{item.subLabel}</Text>
            </Pressable>
          );
        }}
        ListFooterComponent={() => {
          return (
            <Pressable
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                backgroundColor: colors.background,
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
                gap: 4,
              }}
              onPress={() => {
                locals.toggleShowInputs();
                locals.toggleSearch();
              }}
            >
              <Icons name="pencil-outline" size={18} />
              <Text>Add New User</Text>
            </Pressable>
          );
        }}
      />
    );
  }),
);

const handleOnSearchValueChange = (
  debouncer: DebounceFn,
  opportunityOwnerStore: OpportunityOwnersStore,
  value: string,
) => {
  runInAction(() => {
    opportunityOwnerStore.keywordSearchValue = value;
  });
  debouncer(() => {
    opportunityOwnerStore.searchOwners();
  }, 500);
};
