import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Pressable, View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { DependentDropdown, MenuGroup, MenuItem } from '../../../lib';
import { OpportunityStore } from '../../../stores';

interface OperationalRulesDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const OperationalRulesItems: MenuGroup[] = [
  {
    item: { label: 'Combat Arms', value: 'Combat Arms' },
    children: [
      { item: { label: 'Infantry', value: 'Infantry' } },
      { item: { label: 'Armor', value: 'Armor' } },
      { item: { label: 'Field Artillery', value: 'Field Artillery' } },
      { item: { label: 'Air Defense Artillery', value: 'Air Defense Artillery' } },
      { item: { label: 'Aviation', value: 'Aviation' } },
      { item: { label: 'Special Ops', value: 'Special Ops' } },
    ],
  },
  {
    item: { label: 'Combat Support', value: 'Combat Support' },
    children: [
      { item: { label: 'Military Intelligence', value: 'Military Intelligence' } },
      { item: { label: 'Signal Corps', value: 'Signal Corps' } },
      { item: { label: 'Chemical Corps', value: 'Chemical Corps' } },
      { item: { label: 'Military Police', value: 'Military Police' } },
      { item: { label: 'Engineers', value: 'Engineers' } },
    ],
  },
  {
    item: { label: 'Combat Service Support', value: 'Combat Service Support' },
    children: [
      { item: { label: 'Logistics Corps', value: 'Logistics Corps' } },
      { item: { label: 'Transportation Corps', value: 'Transportation Corps' } },
      { item: { label: 'Medical Corps', value: 'Medical Corps' } },
      { item: { label: 'Ordnance Corps', value: 'Ordnance Corps' } },
      { item: { label: 'Quartermaster Corps', value: 'Quartermaster Corps' } },
      { item: { label: 'Finance Corps', value: 'Finance Corps' } },
      { item: { label: "Adjutant General's Corps", value: "Adjutant General's Corps" } },
    ],
  },
];
export const OperationalRulesDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: OperationalRulesDropdownProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <View style={[components.rowStyle]}>
        <Target name={'OperationalRules'}>
          <DependentDropdown
            defaultValues={[
              {
                fieldName: 'operationalRules',
                label: 'Operational Rules',
                value: {
                  label: opportunityStore.opportunity?.operationalRules || 'Unassigned',
                  value: 'unassigned',
                },
              },
              {
                label: undefined,
                value: undefined,
                fieldName: 'operationalRules',
              },
            ]}
            getMenuGroups={() => {
              return OperationalRulesItems;
            }}
            onItemSelected={(item, fieldName) => {
              opportunityStore.setValue(fieldName, item?.value);
              handleDebounce(opportunityStore);
            }}
            labeledDropdownMenuProps={{
              dropdownMenuProps: {
                getEditable: () => editable,
                getMenuItems: () => OperationalRulesItems.map((group) => group.item),
                onItemSelected: (item: MenuItem) => {
                  opportunityStore.setValue('operationalRules', item.value);
                  handleDebounce(opportunityStore);
                },
              },
            }}
          />
        </Target>
      </View>
    );
  }),
);
