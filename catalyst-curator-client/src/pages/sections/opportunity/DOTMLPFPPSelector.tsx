import { observable } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react';
import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView, Pressable } from 'react-native';
import { Menu, Checkbox, Text, Chip, Surface, IconButton } from 'react-native-paper';

const OPTIONS = [
  'Doctrine',
  'Organization',
  'Training',
  'Materiel',
  'Leadership & Education',
  'Personnel',
  'Facilities',
  'Policy',
];

export const DOTMLPFPPSelector = observer(() => {
  const [visible, setVisible] = useState(false);
  const [selected, setSelected] = useState<string[]>([]);

  const toggleOption = (option: string) => {
    setSelected((prev) => (prev.includes(option) ? prev.filter((o) => o !== option) : [...prev, option]));
  };

  const removeTag = (option: string) => {
    setSelected((prev) => prev.filter((o) => o !== option));
  };

  const localStore = useLocalObservable(() => ({
    isMenuOpen: false,
    setIsMenuOpen(value: boolean) {
      this.isMenuOpen = value;
    },
  }));
  return (
    <View style={styles.container}>
      <Pressable onPress={() => localStore.setIsMenuOpen(true)} style={styles.inputBox}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {selected.map((option) => (
            <Chip key={option} onClose={() => removeTag(option)} style={styles.chip}>
              {option}
            </Chip>
          ))}
        </ScrollView>
        <IconButton
          icon={visible ? 'chevron-up' : 'chevron-down'}
          size={20}
          onPress={() => localStore.setIsMenuOpen(!localStore.isMenuOpen)}
        />
      </Pressable>
      <Menu
        visible={localStore.isMenuOpen}
        onDismiss={() => {
          localStore.setIsMenuOpen(false);
        }}
        style={styles.menu}
      >
        <Surface style={styles.menuSurface}>
          <ScrollView>
            <Text style={styles.noChange}>No change</Text>
            {OPTIONS.map((option) => (
              <Pressable key={option} onPress={() => toggleOption(option)} style={styles.optionRow}>
                <Checkbox status={selected.includes(option) ? 'checked' : 'unchecked'} />
                <Text>{option}</Text>
              </Pressable>
            ))}
          </ScrollView>
        </Surface>
      </Menu>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    margin: 16,
  },
  inputBox: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 4,
    minHeight: 48,
  },
  chip: {
    marginHorizontal: 4,
  },
  menu: {
    marginTop: 8,
  },
  menuSurface: {
    padding: 8,
    maxHeight: 300,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  noChange: {
    fontWeight: 'bold',
    paddingBottom: 8,
  },
});
