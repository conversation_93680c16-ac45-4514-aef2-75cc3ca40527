import { withTheme } from 'react-native-paper';
import { OpportunityOwner, OpportunityOwnerStatus } from '../../../services/codegen/types';
import { observer, useLocalObservable } from 'mobx-react';
import { Pressable, View } from 'react-native';
import { But<PERSON>, Text } from '../../../lib';
import Icons from '@expo/vector-icons/MaterialCommunityIcons';
import { OwnerConfirmRemoveDialog } from './OwnerConfirmRemoveDialog';
import { OpportunityStore } from '../../../stores';
import { OpportunityOwnersStore } from '../../../stores/OpportunityOwnersStore';

type OpportunityOwnerCardProps = {
  theme: ReactNativePaper.ThemeProp;
  opportunityOwner: OpportunityOwner;
  opportunityOwnerStore: OpportunityOwnersStore;
  opportunityStore: OpportunityStore;
};

export const OpportunityOwnerCard = withTheme(
  observer(({ opportunityOwner, opportunityOwnerStore, opportunityStore, theme }: OpportunityOwnerCardProps) => {
    const {
      styles: { components },
      colors,
      fonts,
      fontSizes,
    } = theme;

    const { firstName, lastName, emailAddress, phone, altContact, organizationRole, org1, org2, org3, org4 } =
      opportunityOwner.owner;

    const localStore = useLocalObservable(() => ({
      opened: false,
      toggleOpened: () => {
        localStore.opened = !localStore.opened;
      },
      isDialogOpen: false,
      setIsDialogOpen: (value: boolean) => {
        localStore.isDialogOpen = value;
      },
    }));

    const formattedMadePreviousAt = opportunityOwner.madePreviousAt
      ? new Date(opportunityOwner.madePreviousAt).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
      : 'Present';

    const formattedAddedAt = new Date(opportunityOwner.addedAt)?.toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric',
    });

    function getOrganizationText() {
      if (org1 || org2 || org3 || org4) {
        return [org1, org2, org3, org4].filter(Boolean).join(' / ');
      }
      return undefined;
    }

    const isRemoved = opportunityOwner.isRemoved;
    const isPrevious = opportunityOwner.status === OpportunityOwnerStatus.Previous;

    const organizationText = getOrganizationText();
    return (
      <>
        <View
          style={{
            borderColor: colors.border,
            borderWidth: 1,
            borderRadius: 4,
            padding: 16,
            zIndex: -1,
            gap: 8,
            backgroundColor: colors.greyBackgroundColor,
          }}
        >
          <View style={{ justifyContent: 'space-between', flexDirection: 'row', alignItems: 'center' }}>
            <Text style={[fonts.medium]}>
              {lastName}, {firstName}
            </Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              {opportunityOwner.status !== OpportunityOwnerStatus.Initial ? (
                <Text style={{ color: colors.secondaryTextColor }}>
                  {formattedAddedAt} - {formattedMadePreviousAt}
                </Text>
              ) : null}
              <Pressable onPress={localStore.toggleOpened}>
                {localStore.opened ? (
                  <Icons name="chevron-up" size={24} color={colors.secondaryTextColor} />
                ) : (
                  <Icons name="chevron-down" size={24} color={colors.secondaryTextColor} />
                )}
              </Pressable>
            </View>
          </View>
          {localStore.opened ? (
            <View style={{ borderTopWidth: 1, borderTopColor: colors.border, paddingTop: 8, gap: 16 }}>
              <View style={{ flexDirection: 'row', gap: 8 }}>
                <LabeledText label="First Name" text={firstName} />
                <LabeledText label="Last Name" text={firstName} />
              </View>
              <View style={{ flexDirection: 'row', gap: 8 }}>
                <LabeledText label="Email" text={emailAddress} />
                {altContact ? <LabeledText label="Alternative Email" text={altContact} /> : null}
              </View>
              <View style={{ flexDirection: 'row', gap: 8 }}>
                {phone ? <LabeledText label="Phone Number" text={phone} /> : null}
                {organizationRole ? <LabeledText label="Organization Role" text={organizationRole} /> : null}
              </View>
              {organizationText ? <LabeledText label="Organization" text={organizationText} /> : null}
              {opportunityOwner.status === OpportunityOwnerStatus.Initial ? (
                <Text style={{ fontSize: 16, color: colors.secondaryTextColor }}>
                  Note: Submitter information is locked. To edit the information, click Add New Owner to update the
                  information and changes accordingly.
                </Text>
              ) : null}
              {isPrevious && !isRemoved ? (
                <View style={{ alignItems: 'flex-end' }}>
                  <Button
                    labelStyle={[
                      { color: colors.negativeColor, textTransform: 'capitalize' },
                      fonts.mediumTitle,
                      fontSizes.xSmall,
                    ]}
                    style={{ height: 24, borderColor: colors.negativeColor }}
                    type="secondary"
                    onPress={() => localStore.setIsDialogOpen(true)}
                  >
                    Remove Owner
                  </Button>
                </View>
              ) : null}
              {isRemoved ? (
                <View style={{ alignItems: 'flex-end' }}>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle, fontSizes.xSmall]}
                    style={{ height: 24, borderColor: colors.buttonPrimary }}
                    type="secondary"
                    onPress={async () => {
                      opportunityOwnerStore.toggleRemovedOwner(opportunityOwner.id);
                      await opportunityStore.refresh();
                      localStore.toggleOpened();
                    }}
                  >
                    Restore Owner
                  </Button>
                </View>
              ) : null}
            </View>
          ) : null}
        </View>
        <OwnerConfirmRemoveDialog
          getVisible={() => {
            return localStore.isDialogOpen;
          }}
          onConfirm={async () => {
            await opportunityOwnerStore.toggleRemovedOwner(opportunityOwner.id);
            await opportunityStore.refresh();
            localStore.setIsDialogOpen(false);
          }}
          onDismiss={() => {
            localStore.setIsDialogOpen(false);
          }}
        />
      </>
    );
  }),
);

type LabeledTextProps = {
  label: string;
  text: string;
  theme: ReactNativePaper.ThemeProp;
};
export const LabeledText = withTheme(({ label, text, theme }: LabeledTextProps) => {
  const { colors } = theme;
  return (
    <View style={{ flex: 1, gap: 4 }}>
      <Text style={{ fontSize: 16, color: colors.secondaryTextColor }}>{label}</Text>
      <Text style={{ fontSize: 16, paddingLeft: 8 }}>{text}</Text>
    </View>
  );
});
