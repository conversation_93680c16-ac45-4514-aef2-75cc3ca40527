import { ColDef, ValueGetterParams } from 'ag-grid-community';
import { useRef } from 'react';
import { View, Pressable } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, Dates, Tooltip } from '../lib';
import { Priority } from '../lib/Priority';
import { ClickableText } from '../lib/ui/atoms/ClickableText';
import { HiddenView } from '../lib/ui/atoms/HiddenView';
import { Text } from '../lib/ui/atoms/Text';
import { Router } from '../platform/Router';
import { MainStackParamList } from '../routing/screens';
import { Opportunity, OpportunityVisibility, SearchOperator } from '../services/codegen/types';
import { ApplicationMetaStore } from '../stores/ApplicationMetaStore';
import OpportunityListStore from '../stores/OpportunityListStore';
import UserStore from '../stores/UserStore';
import { OpportunityStore, TenantStore } from '../stores';
import { mapStatusToColor } from '../lib/OpportunityStatus';
import { Icon } from '../lib/ui/atoms/Icon';
import { RelationshipsDialog } from './sections/opportunity/RelationshipsDialog';
import { IconLabel } from '../lib/ui/atoms/IconLabel';
import { observable } from 'mobx';
import { GridControl } from '../appComponents/web/Grid.web';
import { GridHeader } from '../appComponents/GridHeader';
import { GenericGrid } from '../appComponents/GenericGrid';
import { Checkbox } from '../lib/ui/atoms/Checkbox';
import { useLocalObservable } from 'mobx-react';
import { ArmyModernizationPrioritiesItems } from './sections/opportunity/curated-opportunity-groups/ArmyModernizationPriorities';

interface DashboardProps {
  opportunityListStore: OpportunityListStore;
  router: Router<MainStackParamList>;
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  tenantStore: TenantStore;
}

interface Locals {
  gridControl?: GridControl;
  selectedOpportunityStore?: OpportunityStore;
  setGridControl: (value?: GridControl) => void;
  setSelectedOpportunityStore: (value?: OpportunityStore) => void;
}

export const DashboardPage = withTheme(
  ({ opportunityListStore, userStore, tenantStore, router, theme }: DashboardProps) => {
    const {
      fonts,
      fontSizes,
      styles: { margins, components },
      colors,
    } = theme;
    const localStore = useLocalObservable<Locals>(() => ({
      gridControl: undefined,
      selectedOpportunityStore: undefined,
      setGridControl(value?: GridControl) {
        this.gridControl = value;
      },
      setSelectedOpportunityStore(value?: OpportunityStore) {
        this.selectedOpportunityStore = value;
      },
    }));
    const { userMetaStore } = userStore;
    const colDefsMap = getColDefsMap(theme, tenantStore, localStore, opportunityListStore);

    return (
      <View style={[components.flexAll, margins.BottomL]}>
        <View style={[components.flexAll, components.panelStyle, components.shadow]}>
          <GridHeader
            handleOnDownload={handleOnDownload}
            opportunityListStore={opportunityListStore}
            userMetaStore={userMetaStore}
            userStore={userStore}
            theme={theme}
            title="Opportunities Dashboard"
            titleColor={colors.primary}
            tenantStore={tenantStore}
          />
          <View style={[{ flexDirection: 'row', alignSelf: 'flex-end' }]}>
            <HiddenView
              getVisible={() =>
                opportunityListStore.searchGroups.asArray().length !== 0 ||
                !opportunityListStore.searchFields.isDefault()
              }
            >
              <ClickableText
                style={[components.smallClickableTextStyle, margins.RightML]}
                onPress={() => handleResetFilters(opportunityListStore)}
              >
                RESET FILTERS
              </ClickableText>
            </HiddenView>
            <HiddenView getVisible={() => !!userMetaStore.oppTable?.hasHidden}>
              <ClickableText
                style={[components.smallClickableTextStyle, margins.RightML]}
                onPress={() => handleRestoreHidden(userStore, userMetaStore, localStore)}
              >
                SHOW HIDDEN
              </ClickableText>
            </HiddenView>
            <ClickableText style={[components.smallClickableTextStyle]} onPress={() => handleResetTable(userStore)}>
              RESET TABLE
            </ClickableText>
          </View>
          <GenericGrid<Opportunity>
            theme={theme}
            store={opportunityListStore}
            router={router}
            userStore={userStore}
            colDefsMap={colDefsMap}
            sortableCols={sortableCols}
            onRowPress={(router, event, opportunityListStore) => {
              opportunityListStore.selection = event.data.id;
              handlePressOpportunity(event.data.id, router);
            }}
            getGridControl={() => localStore.gridControl}
            onGridReady={(gridControl) => {
              localStore.setGridControl(gridControl);
            }}
            table={userMetaStore.oppTable}
          />
        </View>
        <RelationshipsDialog
          getOpportunityStore={() => localStore.selectedOpportunityStore}
          onClose={() => {
            const id = localStore.selectedOpportunityStore?.opportunity?.id;
            // remove the store frome the cache since we staying on the same page
            id && OpportunityStore.removeStoreForId(id);
            localStore.setSelectedOpportunityStore(undefined);
          }}
          onPressOpportunity={(id) => {
            localStore.setSelectedOpportunityStore(undefined);
            handlePressOpportunity(id, router);
          }}
        />
      </View>
    );
  },
);

/* Event Handlers */

const handleResetTable = (userStore: UserStore) => {
  userStore.resetOpTableMeta();
};

const handleResetFilters = (store: OpportunityListStore) => {
  store.resetSearchValues();
  store.queryItems();
};

const handleRestoreHidden = (userStore: UserStore, userMetaStore: ApplicationMetaStore, localStore: Locals) => {
  userMetaStore.oppTable?.showAllColumns();
  localStore.gridControl?.showAllColumns();
  userStore.saveUserApplicationMeta();
};

const handlePressOpportunity = (id: string, router?: Router<MainStackParamList>) => {
  router?.navigate('curation', { id });
};

/* End Event Handlers */

/* 
  Note this function uses observables - once pased to the 'Grid', which is an observer,
  changes here will wil rerender the grid, so use carefully
 */
const handleOnDownload = (
  opportunityListStore: OpportunityListStore,
  applicationMetaStore: ApplicationMetaStore,
  allFields = false,
) => {
  const year = new Date().getFullYear();
  const month = new Date().getMonth() + 1;
  const day = new Date().getDate();
  const fileName: string = `${[year, month, day].join('-')}-opportunities.xlsx`;
  const fields = !allFields
    ? applicationMetaStore.oppTable?.tableMeta?.cols?.reduce((prev, currrent) => {
        if (!currrent.hidden) {
          prev.push(currrent.colId);
        }
        return prev;
      }, [] as string[])
    : undefined;
  opportunityListStore.handleDownload(fileName, fields);
};

const sortableCols = [
  'priority',
  'status',
  'title',
  'lastCurated',
  'createdAt',
  'org1',
  'solutionPathway',
  'statusNotes',
  'campaign',
  'relatedOpportunityCount',
  'function',
  'visibility',
  'armyModernizationPriority',
  'echelonApplicability',
  'transitionInContactLineOfEffort',
  'operationalRules',
  'capabilityArea',
];

export interface GridColDef extends ColDef {}

// Note the width values here are defaults and are overridden by the defaults in tenantStore or the tenant's metadata (or the user's metadata)
const getColDefsMap = (
  theme: ReactNativePaper.ThemeProp,
  tenantStore: TenantStore,
  localStore: Locals,
  opportunityListStore: OpportunityListStore,
): {
  [key: string]: ColDef;
} => {
  const filterStores = opportunityListStore.listFilterStores;
  const {
    warFightingFunctionFilterStore,
    campaignInfoFilterStore,
    priorityFilterInfo,
    statusFilterInfo,
    relationshipsFilterInfo,
  } = filterStores;
  const warFightingFunctionFilterInfo = warFightingFunctionFilterStore;
  warFightingFunctionFilterInfo.info = tenantStore.tenantConfig.fields?.opportunity.function?.values
    ? [
        {
          label: 'Unassigned',
          searchField: { fieldNames: ['function'], searchValue: null, operator: SearchOperator.Eq },
        },
        ...tenantStore.tenantConfig.fields?.opportunity.function.values?.map((functionVal) => ({
          label: functionVal.label,
          searchField: { fieldNames: ['function'], searchValue: functionVal.label, operator: SearchOperator.Match },
        })),
      ]
    : [];

  const {
    colors,
    fontSizes,
    styles: { fonts, components, margins, paddings },
  } = theme;

  return {
    priority: {
      field: 'priority',
      headerName: 'Priority',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const priority = params.data?.priority || Priority.NONE;
        return Priority[priority];
      },
      headerComponentParams: {
        filterInfo: priorityFilterInfo,
      },
    },
    status: {
      field: 'status',
      headerName: 'Status',
      cellRenderer: (params: any) => {
        const status = params.data?.status;
        return <Text style={[{ color: mapStatusToColor(theme, status) }, fonts.bold]}>{status}</Text>;
      },
      headerComponentParams: {
        filterInfo: statusFilterInfo,
      },
    },
    relatedOpportunityCount: {
      field: 'relatedOpportunityCount',
      headerName: 'Relationships',
      headerTooltip: 'Relationships',
      headerComponentParams: {
        headerRenderer: () => (
          <View>
            <Icon name="file-tree" size={23} style={[{ alignSelf: 'center' }]} />
          </View>
        ),
        filterInfo: relationshipsFilterInfo,
      },
      cellStyle: { display: 'flex', justifyContent: 'center', alignItems: 'flex-start' },
      cellRenderer: (params: any) => {
        if (!params.data) return null;
        const { relatedOpportunityCount, parentOpportunityCount, id } = params.data;
        return (
          <Pressable
            onPress={() => {
              const opportunityStore = OpportunityStore.getStoreForId(id);
              opportunityStore.initialize();
              localStore.setSelectedOpportunityStore(opportunityStore);
            }}
          >
            <IconLabel
              style={[
                components.buttonSecondaryStyle,
                parentOpportunityCount > 0 ? {} : { backgroundColor: theme.colors.accent, borderWidth: 0 },
                margins.None,
                paddings.RightMS,
                paddings.VerticalS,
                { width: 50 },
              ]}
              textStyle={components.buttonSecondaryTextStyle}
            >
              {relatedOpportunityCount}
            </IconLabel>
          </Pressable>
        );
      },
      onCellClicked: () => {},
      autoHeight: true,
    },
    title: {
      field: 'title',
      headerName: 'Problem Title',
      autoHeight: true,
      wrapText: true,
    },
    function: {
      field: 'function',
      headerName: tenantStore.tenantConfig.fields?.opportunity.function?.fieldLabel,
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: warFightingFunctionFilterInfo,
      },
    },
    campaign: {
      field: 'campaign',
      headerName: tenantStore.tenantConfig.fields?.opportunity?.campaign?.fieldLabel
        ? tenantStore.tenantConfig.fields?.opportunity.campaign.fieldLabel
        : 'Event',
      autoHeight: true,
      wrapText: true,
      headerComponentParams: {
        filterInfo: campaignInfoFilterStore,
      },
    },
    lastCurated: {
      field: 'lastCurated',
      headerName: 'Last Curated',
      cellRenderer: (params: any) => {
        if (!params.data?.curationInfo?.lastCurated) {
          return (
            <View style={{ justifyContent: 'center', alignItems: 'center', display: 'flex' }}>
              <Button
                compact={true}
                style={[components.auxButton3Style, margins.None, { width: 70 }]}
                labelStyle={components.auxButton3TextStyle}
              >
                New
              </Button>
            </View>
          );
        }
        return Dates.asSimpleDateString(params.data?.lastCurated);
      },
    },
    createdAt: {
      field: 'createdAt',
      headerName: 'Created',
      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return Dates.asSimpleDateString(params.data?.createdAt);
      },
    },
    org1: {
      field: 'org1',
      headerName: 'Org / Team',
      autoHeight: true,
      wrapText: true,

      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        const org1 = params.data?.org1;
        const org2 = params.data?.org2;
        return org1 || org2 ? `${org1 || 'N/A'}${org2 ? ' / ' + org2 : ''}` : 'N/A';
      },
    },
    stakeholders: {
      field: 'stakeholders',
      headerName: 'Stakeholders',
      autoHeight: true,
      wrapText: true,

      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return params.data?.stakeholders.map((stakeholder) => (stakeholder.name ? `${stakeholder.name}` : ''));
      },
    },
    armyModernizationPriority: {
      field: 'armyModernizationPriority',
      headerName: 'Army Modernizations',
      autoHeight: true,
      wrapText: true,
    },
    echelonApplicability: {
      field: 'echelonApplicability',
      headerName: 'Echelon Applicability',
      autoHeight: true,
      wrapText: true,
    },
    transitionInContactLineOfEffort: {
      field: 'transitionInContactLineOfEffort',
      headerName: 'TiC Line of Effort',
      autoHeight: true,
      wrapText: true,
    },
    operationalRules: {
      field: 'operationalRules',
      headerName: 'Operational Rules',
      autoHeight: true,
      wrapText: true,
    },
    capabilityArea: {
      field: 'capabilityArea',
      headerName: 'Capability Area',
      autoHeight: true,
      wrapText: true,
    },
    categories: {
      field: 'categories',
      headerName: 'Custom Tags',
      autoHeight: true,
      wrapText: true,

      valueGetter: (params: ValueGetterParams<Opportunity>) => {
        return params.data?.categories.map((category) => (category.name ? `${category.name}` : ''));
      },
    },
    solutionPathway: {
      field: 'solutionPathway',
      headerName: 'Solution Pathway',
      autoHeight: true,
      wrapText: true,
    },
    statusNotes: {
      field: 'statusNotes',
      headerName: 'Status Notes',
      autoHeight: true,
      wrapText: true,
    },
    visibility: {
      field: 'visibility',
      headerName: 'Private',
      cellStyle: { display: 'flex', justifyContent: 'center', alignItems: 'flex-start' },
      onCellClicked: () => {},
      cellRenderer: (params: any) => {
        const locals = useRef(
          observable({ isChecked: params.data?.visibility === OpportunityVisibility.Private }),
        ).current;

        return (
          <Checkbox
            getChecked={() => locals.isChecked}
            onChecked={async () => {
              const opportunityStore = OpportunityStore.getStoreForId(params.data.id);
              opportunityStore.setValue('visibility', OpportunityVisibility.Private);
              await opportunityStore.saveOpportunity();
              locals.isChecked = true;
            }}
            onUnchecked={() => {
              const opportunityStore = OpportunityStore.getStoreForId(params.data.id);
              opportunityStore.setValue('visibility', OpportunityVisibility.All);
              opportunityStore.saveOpportunity();
              locals.isChecked = false;
            }}
            label={null}
            style={[{ alignSelf: 'center' }]}
          />
        );
      },
      headerComponentParams: {
        headerRenderer: () => (
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'flex-start',
              gap: 4,
            }}
          >
            <Text style={[components.dataTableHeaderTextStyle, fontSizes.small, { alignSelf: 'center' }]}>Private</Text>
            <Tooltip
              offsetY={-329}
              useTopPointer={true}
              text={`By selecting the Private button, you opt to keep the submission and related opportunity data at your unit level for further review and development. \nYour higher headquarters and other Army organizations will be unable to view the submission and opportunity until you opt out of Private mode.`}
            >
              <Icon name="help-circle" color={colors.textSecondary} size={20} />
            </Tooltip>
          </View>
        ),
      },
    },
  } as Record<string, any>;
};
