import { Owner } from '../services/codegen/types';

export class OwnerFormat {
  static formatOwnerOrg(owner?: Owner) {
    if (!owner) return 'n/a';
    return owner?.org1 || owner?.org2 ? `${owner?.org1 || 'n/a'}${owner.org2 ? ' / ' + owner.org2 : ''}` : 'n/a';
  }

  static formatOwner(owner?: Owner) {
    if (!owner) return 'n/a';
    return `${owner?.lastName || ''}, ${owner?.firstName || ''}`;
  }

  static formatEmails(owner?: Owner) {
    if (!owner) return 'n/a';
    return owner?.emailAddress || owner?.altContact
      ? `${owner.emailAddress || 'n/a'}${owner.altContact ? ', ' + owner.altContact : ''}`
      : 'n/a';
  }
}
